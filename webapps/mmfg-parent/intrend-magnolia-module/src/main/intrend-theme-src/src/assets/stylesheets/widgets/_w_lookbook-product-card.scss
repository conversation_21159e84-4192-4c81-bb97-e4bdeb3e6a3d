.w-lookbook-product-card {
  .l-p-c-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-width: rem(170);

    .product-card {
      &__img-container {
        position: relative;
        min-width: rem(170);
        margin-bottom: rem(7);

        .wishlist-icon-container {
          position: absolute;
          font-size: rem(16);
          right: rem(10);
          top: rem(10);
          margin: rem(0);
          // margin-right: 10px;
          z-index: 1;
          text-decoration: none;
          &:hover {
            cursor: pointer;
          }
          a {
            text-decoration: none;
          }
        }
      }

      &__info-container {
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: space-between;

        &__element-title {
          font-size: rem(12);
          font-weight: $font-regular;
          line-height: rem(18);
          letter-spacing: 0.02em;
          text-align: left;

          a {
            text-decoration: none;
          }

        }

        &__element-size {
          margin-top: rem(11);
          margin-bottom: rem(7);

          .c-s-i__title-one-size {
            display: flex;
            width: auto;
            height: rem(44);
            padding: rem(12) rem(13);
            align-items: center;
            gap: rem(175);
            flex-shrink: 0;

            border:rem(1) solid $grey;

              h2 {
                color: #000;
                font-family: Poppins;
                font-size: rem(13);
                font-style: normal;
                font-weight: 500;
                line-height: normal;
                letter-spacing: rem(0.26);
              }
          }

        }

        &__element-button {
          .c-s-i__cta {
            display: flex;
            flex-direction: row;

            span:not(.icon-cart) {
              margin-left: rem(10) !important;
              @media #{$until-large} {
                position: static !important;
                width: auto !important;
                height: auto !important;
                overflow: visible !important;
                clip: auto !important;
              }
            }
          }
        }

        &-price {
          font-size: rem(12);
          font-weight: $font-regular;
          line-height: rem(18);
          letter-spacing: 0.02em;
          text-align: left;
          flex: 1;

          .product-card__price {
            &.price-discount {
              color: $red;
            }

            &.price-full:not(:last-child) {
              text-decoration: line-through;
            }
          }

        }

      }
    }
  }
}

@media #{$from-large} {

  .w-lookbook-product-card {
    &:not(:last-child) {
      margin-right: rem(15);
    }

    .l-p-c-wrapper {
      width: rem(208);

      .product-card {

        &__info-container {
          display: flex;
          flex-direction: column;

          &__interactables {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            margin-top: rem(11);

            .product-card__info-container__element-size {
              width: 100%;
              max-width: rem(150);
              max-height: rem(44);
              margin: 0;
              margin-right: rem(7);
            }

            .product-card__info-container__element-button {
              display: flex;
              flex-direction: row;

              .c-s-i__cta {
                width: rem(50);
                height: rem(45);

                span.icon-cart {
                  font-size: rem(20) !important;
                }

              }

            }
          }
        }
      }
    }

    .select2-container {
      .select2-dropdown {
        bottom: 200%;

        .select2-results {

          .select2-results__options {

            overflow: scroll;

            -ms-overflow-style: none; // valido per edge
            scrollbar-width: none; // valido per chrome/firefox

            &:-webkit-scrollbar {
              // valido per chrome e safari
              display: none;
            }
          }
        }
      }
    }
  }
}
