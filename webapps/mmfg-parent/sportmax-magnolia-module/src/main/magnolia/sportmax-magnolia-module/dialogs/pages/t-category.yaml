label: Category page
form:
  $type: tabbedForm
  tabs:
    tabGeneral: !include:/core-magnolia-module/dialogs/dialogs-includes/tabs/tab-generic-page-withsubtitle.yaml
    tabHeader: !include:/sportmax-magnolia-module/dialogs-includes/header-behaviour.yaml
    tabSeo: !include:/core-magnolia-module/dialogs/dialogs-includes/tabs/tab-seo.yaml
    tabSeoCurvy: !include:/sportmax-magnolia-module/dialogs-includes/seo-properties-curvy.yaml
    tabSocial: !include:/core-magnolia-module/dialogs/dialogs-includes/tabs/tab-social.yaml
    tabVisibility: !include:/sportmax-magnolia-module/dialogs-includes/tab-visibility.yaml
    tabFooter: !include:/core-magnolia-module/dialogs/dialogs-includes/footer-behavior.yaml
    tabNav: !include:/core-magnolia-module/dialogs/dialogs-includes/tabs/tab-nav-category.yaml
    tabCategory:
      label: Category
      fields:
        - name: categorycode
          $type: textField
          label: Category code
          rows: 1
        - name: enableFocusModeGrid
          $type: checkBoxField
          label: Enable focus mode grid
          buttonLabel: Enable focus mode grid
        - name: disableFocusModeGrid
          $type: checkBoxField
          defaultValue: false
          label: Disable focus mode
          buttonLabel: Disable focus mode on MaxMara Site
        - name: focusNextPages
          $type: checkBoxField
          defaultValue: false
          label: Focus next pages
          buttonLabel: Enable focus mode for next pages (from page 1 onwars) on MaxMara Site.
          description: It will be ineffective if the "Disable focus mode" flag is checked.
        - name: titleCurvy
          $type: textField
          label: Title curvy (DT)
          i18n: false
        - name: categoryText
          $type: textField
          label: SEO text
          rows: 4
        - name: categoryTextCurvy
          $type: textField
          label: SEO text curvy( DT)
          rows: 4
        - name: seoTextShortDescription
          $type: richTextField
          label: Category SEO Text (MC) - Short Description
          source: true
          alignment: true
          description: This text will be shown below the category Title. It will be the first part of long description.
        - name: seoTextLongDescription
          $type: richTextField
          label: Category SEO Text (MC) - Long Description
          source: true
          alignment: true
          description: This will contain the entire seo text description and it will be displayed at the bottom of category page
        - name: categoryGroup
          $type: radioButtonGroupField
          type: java.lang.String
          label: Category group
          datasource:
            $type: optionListDatasource
            options:
              - label: None
                value: none
              - label: Top
                value: top
              - label: Bottom
                value: bottom
              - label: Coats
                value: coats
        - name: showRelatedCategory
          $type: checkBoxField
          label: Show in listing
          buttonLabel: Show related category in this page
        - name: showCategoryInRelatedCategory
          $type: checkBoxField
          label: Show category in cross-category (MC)
          buttonLabel: Show category in cross-category (MC)
        - name: showNewArrival
          $type: checkBoxField
          label: Show new arrival products in listing
          buttonLabel: Show new arrival in this page
        - name: highlight
          $type: checkBoxField
          label: Highlight
          buttonLabel: Check to highlight nav link
        - name: special
          $type: checkBoxField
          label: Special link (DT-MC)
          buttonLabel: Mark this link as special
          description: Link will have special layout (lowercase and italic for DT, "new" tag on MC)
        - name: disableSwitchCurvy
          $type: checkBoxField
          label: Disable Curvy
          buttonLabel: Disable Curvy/Regular Switch for current page
          description: Feature available only for site with regular/curvy catalog (DT)
        - name: hideCurvy
          $type: checkBoxField
          label: Hide for curvy
          buttonLabel: Hide page in curvy menu and listing banner SEO link
          description: Feature available only for site with regular/curvy catalog (DT)
        - name: hideRegular
          $type: checkBoxField
          label: Hide for regular
          buttonLabel: Hide page in regular menu and listing banner SEO link
          description: Feature available only for site with regular/curvy catalog (DT)
        - name: image
          $type: damLinkField
          label: Image
          description: Image to display in other category pages
          textInputAllowed: true
          # appName: mediabrowser
          # targetWorkspace: media
          # identifierToPathConverter:
          #   $type: net.sourceforge.openutils.mgnlmedia.media.fields.MediaIdentifierToPathConverter
          # contentPreviewDefinition:
          #   contentPreviewClass: net.sourceforge.openutils.mgnlmedia.media.fields.MediaFilePreviewComponent
        - name: saleImage
          $type: damLinkField
          label: Sale Image
          description: Image to display in sale preview page
          textInputAllowed: true
          # appName: mediabrowser
          # targetWorkspace: media
          # identifierToPathConverter:
          #   $type: net.sourceforge.openutils.mgnlmedia.media.fields.MediaIdentifierToPathConverter
          # contentPreviewDefinition:
          #   contentPreviewClass: net.sourceforge.openutils.mgnlmedia.media.fields.MediaFilePreviewComponent
        - name: saleImageCurvy
          $type: damLinkField
          label: Curvy Sale Image
          description: Image to display in sale preview page when navigating in curvy mode
          textInputAllowed: true
          # appName: mediabrowser
          # targetWorkspace: media
          # identifierToPathConverter:
          #   $type: net.sourceforge.openutils.mgnlmedia.media.fields.MediaIdentifierToPathConverter
          # contentPreviewDefinition:
          #   contentPreviewClass: net.sourceforge.openutils.mgnlmedia.media.fields.MediaFilePreviewComponent
        - name: linkLabel
          $type: textField
          label: Link label
          i18n: false
        # - name: ctaPosition
        #   $type: info.magnolia.ui.form.field.definition.SelectFieldDefinition
        #   label: CTA position
        #   sortOptions: false
        #   options: !include /diffusionetessile/dialogs-includes/marella-magnolia-module/nine-positions.yaml
        - name: ctaStyle
          $type: radioButtonGroupField
          type: java.lang.String
          label: CTA style
          description: Choose CTA text color and background
          datasource:
            $type: optionListDatasource
            options:
              - value: white
                label: Bianco su nero
              - value: highlight
                label: Nero su giallo
              - value: reverse
                label: Nero su bianco
        - name: ctaSet
          $type: checkBoxField
          label: CTA set
          buttonLabel: Remove curvy
          description: Flag this checkbox if you don't want the curvy external page
        - name: ctaColor
          $type: textField
          label: Cta text color
          description: Insert the hex color code to modify cta text color (e.g #ffff00 )
        - name: ctaBgColor
          $type: textField
          label: Cta background color
          description: Insert the hex color code to modify cta background color (e.g #ffff00 )
    tabFilter:
      label: Filtering
      fields:
        - name: facets
          $type: jcrMultiField
          label: Filters
          field:
            name: compositeField
            $type: compositeField
            properties:
              - name: facet
                $type: textField
                label: Facet
              - name: value
                $type: textField
                label: Value
        - name: filteringProducts
          $type: textField
          label: Products SKUs
          description: Insert manual SKUs to filter category by product codes
          rows: 4
    tabCategoryFolded:
      label: Category Folded PB
      fields:
        - name: enableproductsForRow
          $type: checkBoxField
          label: Enable value
          buttonLabel: Enable three products for row
    tabCategoryCollection:
      label: Category Collection MR/MA
      fields:
        - name: collectionType
          $type: comboBoxField
          label: MA collection
          description: "MA: All, Marella, Emme"
          datasource:
            $type: optionListDatasource
            options:
              - name: all
                value: all
                label: All
              - name: marella
                value: marella
                label: Marella
              - name: emme
                value: emme
                label: Emme
    tabBanner:
      label: Banner
      fields:
        - name: bannerTitle
          $type: textField
          label: Title
          rows: 1
        - name: bannerSubtitle
          $type: textField
          label: Subtitle
          rows: 1
        - name: bannerImage
          $type: damLinkField
          label: Banner Image
          description: Image to display in sale preview page or default Image to display for SPX, MA
          textInputAllowed: true
          # appName: mediabrowser
          # targetWorkspace: media
          # identifierToPathConverter:
          #   $type: net.sourceforge.openutils.mgnlmedia.media.fields.MediaIdentifierToPathConverter
          # contentPreviewDefinition:
          #   contentPreviewClass: net.sourceforge.openutils.mgnlmedia.media.fields.MediaFilePreviewComponent
        - name: bannerImageMedium
          $type: damLinkField
          label: Banner Image Medium
          description: Image to display in medium resolution (ususally tablet) for SPX, MA
          textInputAllowed: true
          # appName: mediabrowser
          # targetWorkspace: media
          # identifierToPathConverter:
          #   $type: net.sourceforge.openutils.mgnlmedia.media.fields.MediaIdentifierToPathConverter
          # contentPreviewDefinition:
          #   contentPreviewClass: net.sourceforge.openutils.mgnlmedia.media.fields.MediaFilePreviewComponent
        - name: bannerImageSmall
          $type: damLinkField
          label: Banner Image Small
          description: Image to display in small resolution (usually smartphone) for SPX, MA
          textInputAllowed: true
          # appName: mediabrowser
          # targetWorkspace: media
          # identifierToPathConverter:
          #   $type: net.sourceforge.openutils.mgnlmedia.media.fields.MediaIdentifierToPathConverter
          # contentPreviewDefinition:
          #   contentPreviewClass: net.sourceforge.openutils.mgnlmedia.media.fields.MediaFilePreviewComponent
        - name: isAnimated (MA)
          $type: checkBoxField
          label: Is a animated
          buttonLabel: Check if this image is an animated gif
        - name: bannerLink
          $type: pageLinkField
          textInputAllowed: true
          label: Link
          # appName: pages
          # identifierToPathConverter:
          #   $type: net.sourceforge.openutils.mgnlcontrols.fields.OptionalIdentifierToPathConverter
        - name: textColor
          $type: listSelectField
          label: Text color
          datasource:
            $type: optionListDatasource
            options:
              - name: light
                value: light
                label: Light
              - name: dark
                value: dark
                label: Dark
              - name: pink
                value: pink
                label: Pink
              - name: grey
                value: grey
                label: Grey
    tabVideo1:
      label: Banner Video (MA)
      fields: !include:/core-magnolia-module/dialogs/dialogs-includes/video-basic-options.yaml
    tabVideo:
      label: Video
      fields:
        - name: videoProducts
          $type: textField
          label: Products
          description: for this products show video instead of default image
          rows: 10
    tabSingleSaleCategory:
      label: Single sale category
      fields:
        - name: saleTitle
          $type: textField
          label: Sale title
          description: Titile for single cateogory during sale period. Original titile will be left on navbar and this will be written into dropdown.
          rows: 1
        - name: saleDropdownImage
          $type: damLinkField
          label: Sale dropdown image
          description: Image to display in sale dropdown (SPX, MA)
          textInputAllowed: true
          # appName: mediabrowser
          # targetWorkspace: media
          # identifierToPathConverter:
          #   $type: net.sourceforge.openutils.mgnlmedia.media.fields.MediaIdentifierToPathConverter
          # contentPreviewDefinition:
          #   contentPreviewClass: net.sourceforge.openutils.mgnlmedia.media.fields.MediaFilePreviewComponent
        - name: saleDropdownImageTitle
          $type: textField
          label: Image title
          rows: 1
        - name: saleDropdownImageSubtitle
          $type: textField
          label: Image subtitle
          rows: 1
        - name: salespage
          $type: checkBoxField
          label: Is Sales Page
          buttonLabel: Mark this category page as sales page
          default: false
        - name: redirectlink
          $type: pageLinkField
          textInputAllowed: true
          label: Redirect Link
          # appName: pages
          # identifierToPathConverter:
          #   $type: net.sourceforge.openutils.mgnlcontrols.fields.OptionalIdentifierToPathConverter
          # description: Rediret to this page if site is not in sale mode

    tabEditorialListing:
      label: Editorial listing
      fields:
        - name: listingFakeModelLink
          $type: pageLinkField
          label: Optional link on editorial image
          description: Add only SKU or editorial page link
          textInputAllowed: true
          # appName: pages    
          # identifierToPathConverter:    
          #    $type: net.sourceforge.openutils.mgnlcontrols.fields.OptionalIdentifierToPathConverter   
        - name: listingFakeModelImage
          $type: damLinkField
          label: Editorial image
          description: Editorial image
          textInputAllowed: true
          # appName: mediabrowser   
          # targetWorkspace: media    
          # identifierToPathConverter:    
          #   $type: net.sourceforge.openutils.mgnlmedia.media.fields.MediaIdentifierToPathConverter    
          # contentPreviewDefinition:   
          #   contentPreviewClass: net.sourceforge.openutils.mgnlmedia.media.fields.MediaFilePreviewComponent
        - name: pageSize
          $type: textField
          label: Page size
          rows: 1

    tabAlgoliaListing:
      label: Algolia
      fields:
        - name: algoliaenabled
          $type: checkBoxField
          label: Algolia Enabled
          buttonLabel: Check to enable Algolia in this category page
        - name: algoliaContextRule
          $type: textField
          label: Algolia Context Rule
          rows: 1
        - name: algoliaCurvyContextRule
          $type: textField
          label: Algolia Curvy Context Rule (only for DT)
          rows: 1

    tabTarget2Sell: !include:/core-magnolia-module/dialogs/dialogs-includes/tabs/tab-target2sell.yaml

