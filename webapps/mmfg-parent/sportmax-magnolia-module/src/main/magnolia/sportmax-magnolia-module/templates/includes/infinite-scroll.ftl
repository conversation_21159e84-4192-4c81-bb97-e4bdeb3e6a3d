[#assign pagination =searchPageData.pagination /]
[#assign numberOfPages = (pagination.numberOfPages)!0 /]
[#assign currentPage =pagination.currentPage /]
[#if !actpage?has_content]
    [#assign actpage = ctx.request.getAttribute("javax.servlet.forward.request_uri")?replace(ctx.contextPath, "")]
[/#if]

[#assign baseUrl = mmfgfn.baseurl() + mmfgfn.link(actpage) + '?page=']
[#assign prevPageUrl = (currentPage > 0)?then(baseUrl + (currentPage - 1), "")]
[#assign nextPageUrl = (currentPage + 1 < numberOfPages)?then(baseUrl + (currentPage + 1), "")]

[#assign productsCount = (pagination.totalNumberOfResults)!0 - (pagination.pageSize * (currentPage + 1)) /]
    <section class="c-l__cta infinite-scroll" data-js-component="PaginationComponent" data-infinite-scroll-trigger="">
        <button class="cta-primary c-l__cta_btn js-infinite-scroll${(numberOfPages > (currentPage + 1))?then('', ' d-none')}" data-page="${currentPage + 1}"
            data-infinite-scroll="" data-infinite-scroll-lastpage="${numberOfPages - 1}">
            <span class="bg-top"></span>
            <span class="text" data-cta-text="${mmfgfn.msg('viewall.title')}  ${pagination.totalNumberOfResults}">${mmfgfn.msg("viewall.title")}  ${pagination.totalNumberOfResults}</span>
            <span class="bg-bottom"></span>
        </button>
        [#-- next & previous  page link for SEO --]
        [#if nextPageUrl?has_content]
          <a class="hidden" href="${nextPageUrl}"></a>
        [/#if]
        [#if prevPageUrl?has_content]
          <a class="hidden" href="${prevPageUrl}"></a>
        [/#if]
    </section>