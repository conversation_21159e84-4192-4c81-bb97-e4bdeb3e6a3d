[#assign categoryFaqsIsNotEmpty = categoryFaqs?? && categoryFaqs?has_content]
[#assign categoriesResultsIsNotEmpty = categories?? && categories?has_content && categories.results?? && categories.results?has_content]
[#assign searchFaqsIsNotEmpty = searchFaqs?? && searchFaqs?has_content]
[#assign categoryList = categories ]

[#if categoryFaqsIsNotEmpty]
    [#assign categoryTraslated = categoryName]
    [#if categoriesResultsIsNotEmpty]
        [#list categories.results as category]
            [#if category.name == categoryName]
                [#assign title = category.seoTitle]
                [#assign metadescription = category.seoDescription]
                [#assign categoryTraslated = category.title]
            [/#if]
        [/#list]
    [/#if]
[/#if]

[#include "./page.ftl"]
[#include "../includes/faq/yext-faq-list.ftl"]

[@page content=content controller="YextFaqController" synchLoadControllerCss=true ]
    <div data-js-component="FaqComponent">
        [#include "../includes/faq/yext-faq-header.ftl" /]
        <div class="yext-faq__container">
            [#if popularFaqs?? && popularFaqs?has_content]
                [#if ctx.getParameter('q')!?has_content && searchFaqsIsNotEmpty && !searchFaqs.results?has_content]
                    <div class="yext-faq__no-result">
                      <p class="yext-faq__no-result__title">${mmfgfn.msg('spelling.check.title', ctx.getParameter('q')!?html)}</p>
                      <p class="yext-faq__no-result__subtitle">${mmfgfn.msg('spelling.check.subtitle')}</p>
                    </div>
                [/#if]
                <h1 class="yext-faq__title">${mmfgfn.msg('yext.faq.body.title')}</h1>
                [@yextFaqList faqs=popularFaqs faqLimit=resultsPerPage!0 /]
            [#elseif searchFaqsIsNotEmpty]
                [#assign actualPageNumber = pageNumber!1]
                [#assign resultsPerPage = resultsPerPage!0]
                [#assign minPageNumber = (resultsPerPage * actualPageNumber) - (resultsPerPage - 1)]
                [#assign maxPageNumber = ((resultsPerPage * actualPageNumber) gt faqResultsCount)?then(faqResultsCount, resultsPerPage * actualPageNumber)]
                <p class="yext-faq__actualpage">${minPageNumber} - ${maxPageNumber} ${mmfgfn.msg('pagination.of')} ${faqResultsCount}</p>
                [@yextFaqList faqs=searchFaqs faqLimit=resultsPerPage pager=true /]
            [#elseif categoryFaqsIsNotEmpty]
                [@yextFaqList faqs=categoryFaqs cssClasses="margin-top-l" /]
            [/#if]
            [#include "../includes/faq/yext-faq-footer.ftl" /]
        </div>
    </div>
[/@page]