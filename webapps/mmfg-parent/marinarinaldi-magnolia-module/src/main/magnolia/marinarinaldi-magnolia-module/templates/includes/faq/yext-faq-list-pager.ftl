[#include "../ui/a.ftl" ]

[#assign actualPageNumber = pageNumber!1]
[#assign qS = mmfgfn.replaceAll(state.queryString!'', '(&amp;){0,1}page=[0-9]{0,99}', '')]
[#assign baseUrl = mmfgfn.link(state.currentURI)]
[#assign prevBaseUrlpage = "&amp;page=${actualPageNumber - 1}"]
[#assign prevBaseUrl = "${baseUrl}?${qS}${(actualPageNumber == 2)?then('', prevBaseUrlpage)}"]
[#assign prevUrl = "${(actualPageNumber == 1)?then('javascript:void(0)', prevBaseUrl)}"]
[#assign nextBaseUrl = "${baseUrl}?${qS}&amp;page=${actualPageNumber + 1}"]
[#assign nextUrl = "${(actualPageNumber == numberOfPages)?then('javascript:void(0)', nextBaseUrl)}"]
[#assign resultsPerPage = resultsPerPage!0]
[#assign processablePages = numberOfPages - 1]
[#assign currentPageInInitialBlock = actualPageNumber < 2]
[#assign currentPageInFinalBlock = (actualPageNumber < processablePages) && (actualPageNumber > (processablePages - 2)) /]


[#if faqResultsCount gte resultsPerPage && numberOfPages gt 1]
    [#-- used class css Pagination --]
        <div class="c-pagination pagination yext-faq-list-pager">
            [@a_link
                href="${prevUrl}"
                cssClass="c-pag__prev navigation-item pagination-button previous ${(actualPageNumber == 1)?then('disabled', '')}"
            ]
                <i class="icon-chevron-left --s"></i>
                <span class="c-pag__text c-pag__text-previous pages --desktop">${mmfgfn.msg('pager.previous')}</span>
            [/@a_link]

            [#if numberOfPages > 1]
                [#assign reversedPaginationStart = processablePages - 6]
                [#if currentPageInFinalBlock]
                    [#assign start = [1,reversedPaginationStart]?max]
                    [#assign end = processablePages]
                [#elseif currentPageInInitialBlock]
                    [#assign start = 1]
                    [#assign end = [processablePages,start+5]?min]
                [#else]
                    [#assign visiblePages = 2]
                    [#assign start = [actualPageNumber - visiblePages,0]?max ]
                    [#assign end = [processablePages,actualPageNumber + visiblePages]?min ]
                [/#if]

                <div class="pages --desktop">
                    [#if start > 0]
                        [#assign pageUrlpage = "&amp;page=1"]
                        [#assign pageUrl = "${baseUrl}?${qS}"]
                        <a class="page ${(1 == actualPageNumber)?then ('disabled', '')}" href="${(1 == actualPageNumber)?then('javascript:void(0)', pageUrl)}">${1}</a>
                        [#if start > 1]
                            <div class="page">...</div>
                        [/#if]
                    [/#if]

                    [#list start..end as item]
                        [#assign actualPage = item +1]
                        [#assign pageUrlpage = "&amp;page=${actualPage}"]
                        [#assign pageUrl = "${baseUrl}?${qS}${((actualPage!0) == 1)?then('', pageUrlpage)}"]
                        <a class="page ${(actualPage == actualPageNumber)?then ('disabled', '')}" href="${(0 == actualPageNumber)?then('javascript:void(0)', pageUrl)}">${actualPage }</a>
                    [/#list]

                    [#if end < processablePages]
                        [#assign pageUrlpage = "&amp;page=${numberOfPages}"]
                        [#assign pageUrl = "${baseUrl}?${qS}${((actualPage!0) == 1)?then('', pageUrlpage)}"]
                        [#if end < processablePages-1]
                            <div class="page">...</div>
                        [/#if]
                        <a class="page ${(end == actualPageNumber)?then ('disabled', '')}" href="${(numberOfPages == actualPageNumber)?then('javascript:void(0)', pageUrl)}">${pages}</a>
                    [/#if]
                </div>

                <div class="pages --mobile">
                    ${mmfgfn.msg('search.page.currentPage',actualPageNumber,numberOfPages)}
                </div>
            [/#if]

            [@a_link
                href="${nextUrl}"
                cssClass="c-pag__next navigation-item pagination-button next ${(actualPageNumber == numberOfPages)?then(' disabled', '')}"
            ]
                <span class="c-pag__text c-pag__text-next pages --desktop">${mmfgfn.msg('pager.next')}</span>
                <i class="icon-chevron-right --s"></i>
            [/@a_link]
        </div>
[/#if]