[#include "page.ftl" ]
[#include "../includes/ui/a.ftl"]
[#include "../includes/ui/button.ftl"]
[#include "../includes/ui/checkbox.ftl"]
[#include "../includes/ui/radio-button.ftl"]

[@page controller="PrivacyRecallController" mainClass="p-privacy-recall" bare=true isLogoCentered=true content=content ]

  [#assign recallForm = ctx.recallForm /]

  <div class="privacy-recall" data-js-component="PrivacyRecallComponent">
    [#if (ctx.success)!false && !mmfgfn.isEditMode()]
      <p class="privacy-recall__success">${mmfgfn.msg('unsubscribe.applied')}</p>
    [/#if]
    [#if (recallForm.error!false || (ctx.success?? && ctx.success?has_content && ctx.success == false)) && !mmfgfn.isEditMode()]
      <p class="privacy-recall__error">${mmfgfn.msg('unsubscribe.error.subtitle')}</p>
    [/#if]
    <div class="privacy-recall__header">
      <h1>${mmfgfn.msg('privacy.recall.landing.title')}</h1>
      <h2>${mmfgfn.msg('privacy.recall.landing.subtitle')}</h2>
    </div>
    <form method="post" commandName="recallForm" id="recallForm" action="" autocomplete="off">
      [#assign brandName = (recallForm.getRecalledBrandPrivacyData().getBrandName())!'' /]
      <input type='hidden' name="recalledBrandPrivacyData.brandName" value="${brandName}"/>
      [#assign privacyPolicyUrl = "<a href='${mmfgfn.link('/info/legal-area/privacy')}' class='custom-link --underline'>" /]
      [@formCheckbox
        name="privacy"
        label=mmfgfn.msg('register.privacy.agree', privacyPolicyUrl)
        containerClass="privacy-recall__checkbox"
      /]
      <div class="privacy-recall__radios --disabled" data-privacy-radios="">
        <p>${mmfgfn.msg('flag.profile.first')}</p>
        [#assign radioButtonOnePath = (mmfgfn.isUS() || mmfgfn.isCa())?then( 'recalledBrandPrivacyData.newsletter' , 'recalledBrandPrivacyData.profiled') /]
        [#assign radioButtonOneSR = (mmfgfn.isUS())?then(mmfgfn.msg("register.notProfiled.marketing"), mmfgfn.msg("register.profiled.marketing")) /]
        [#assign radioButtonTwoPath = (mmfgfn.isUS() || mmfgfn.isCa())?then( 'recalledBrandPrivacyData.profiled' , 'recalledBrandPrivacyData.newsletter') /]
        [#assign radioButtonTwoSR = (mmfgfn.isUS())?then(mmfgfn.msg("register.profiled.marketing"), mmfgfn.msg("register.notProfiled.marketing")) /]
        <ol>
          <li>
            <p>${mmfgfn.msg('flag.profile.second')}</p>
            <fieldset class="radio-group">
              <legend class="sr-only">${radioButtonOneSR}</legend>
              [@radioButton name=radioButtonOnePath disabled=true value="yes" label=mmfgfn.msg('generic.yes') /]
              [@radioButton name=radioButtonOnePath disabled=true value="no" label=mmfgfn.msg('generic.no') /]
            </fieldset>
          </li>
          <li>
            <p>${mmfgfn.msg('flag.profile.third')}</p>
            <fieldset class="radio-group">
              <legend class="sr-only">${radioButtonTwoSR}</legend>
              [@radioButton name=radioButtonTwoPath disabled=true value="yes" label=mmfgfn.msg('generic.yes') /]
              [@radioButton name=radioButtonTwoPath disabled=true value="no" label=mmfgfn.msg('generic.no') /]
            </fieldset>
          </li>
        </ol>
      </div>
      <p>${mmfgfn.msg('privacy.recall.landing.title2')}</p>
      [@button
        msg=mmfgfn.msg('unsubscribe.apply.recall')
        type="submit"
        disabled=true
      /]
    </form>
    <p class="privacy-recall__contact-us">
      <span>${mmfgfn.msg('unsubscribe.doubts')}</span>
      [@a_link
        href="/info/customer-care/contact-us"
        external=true
      ]
        ${mmfgfn.msg('contactus.title')}
      [/@a_link]
    </p>
  </div>
[/@page]