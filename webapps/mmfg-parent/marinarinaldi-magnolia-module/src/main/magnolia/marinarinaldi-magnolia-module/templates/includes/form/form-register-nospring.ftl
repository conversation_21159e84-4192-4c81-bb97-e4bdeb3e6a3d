[#include "../ui/input.ftl"]
[#include "../ui/radio-button.ftl"]
[#include "../ui/checkbox.ftl"]
[#include "../ui/a.ftl"]
[#include "./form-invisible-captcha.ftl"]

  <form method="POST" id="registerForm" class="form-reg registration__form js-validate-form js-register-form" action="${ctx.contextPath}/register" data-js-component="RegistrationComponent">
    <input type="hidden" name="register" value="true"/>
    <input type="hidden" name="formId" value="registerForm"/>

    <div class="form-reg__main">
    [#if confirm!true]
        <div class="form-group">
          [@input
            label=mmfgfn.msg('register.firstName')
            name="firstName"
            value="${mmfgfn.escapeXml(registerForm.firstName!'')}"
            required=true
            attrs={
              "maxlength":"${FirstName_lenght}"
            }
          /]
        </div>
        <div class="form-group">
          [@input
            label=mmfgfn.msg('register.lastName')
            name="lastName"
            value="${mmfgfn.escapeXml(registerForm.lastName!'')}"
            required=true
            attrs={
              "maxlength":"${LastName_lenght}"
            }
          /]
        </div>
      [/#if]
      <div class="form-group">
        [@input
            name="email"
            id="email"
            label=mmfgfn.msg('address.email')
            value=""
            type="email"
            required=true
            errors=errors!{}
            attrs={
            "maxlength":EmailAdd_lenght!''
            }
          /]
      </div>
      <!--  to be changed with show psw -->
      <div class="form-group password-group">
        <div class="password-wrapper">
          [@input
              name="password"
              id="password"
              label=mmfgfn.msg('register.pwd')
              value=""
              type="password"
              required=true
              errors=errors!{}
              iconClass="icon-visibility-on --pointer --m toggle-password"
              attrs={"maxlength":"50"}
              parentAttrs={"data-js-component":"ShowPasswordComponent"}
            /]
        </div>
      </div>
    
       <div class="form-group privacy-checkbox">
      [#assign privacyPolicyUrl]
        [#include "../ui/a.ftl"]
        [@a_link
          href=mmfgfn.termsLink()
          cssClass="--underline"
          isProperty=true
        /]
      [/#assign]

    [@formCheckbox
      id="param12"
      name="${paramPrivacy!''?has_content?then(paramPrivacy,'privacy')}"
      id="basicPrivacyRegister"
      cssClass="js-register-privacy"
      label="${mmfgfn.msg('register.privacy.agree', privacyPolicyUrl!'')}"
      value="true"
      attrs={
        "data-privacy-checkbox":""
      }
    /]
  </div>

  [#--ACCEPTANCE SECTION--]
  [#assign radioButtonOnePath = (mmfgfn.isUS())?then( 'unProfiledMarketing' , 'profiledMarketing') /]
  [#assign radioButtonOneSR = (mmfgfn.isUS())?then(mmfgfn.msg("register.notProfiled.marketing"), mmfgfn.msg("register.profiled.marketing")) /]
  [#assign radioButtonOneIdYes = (mmfgfn.isUS())?then( 'postageyesNL' , 'postageyes') /]
  [#assign radioButtonOneIdNo = (mmfgfn.isUS())?then( 'postagenoNL' , 'postageno') /]
  [#assign radioButtonTwoPath = (mmfgfn.isUS())?then( 'profiledMarketing' , 'unProfiledMarketing') /]
  [#assign radioButtonTwoSR = (mmfgfn.isUS())?then(mmfgfn.msg("register.profiled.marketing"), mmfgfn.msg("register.notProfiled.marketing")) /]
  [#assign radioButtonTwoIdYes = (mmfgfn.isUS())?then( 'postageyes' , 'postageyesNL') /]
  [#assign radioButtonTwoIdNo = (mmfgfn.isUS())?then( 'postageno' , 'postagenoNL') /]
  <div class="form-reg__acceptance form-consents disabled" data-consents="">
    <div class="form-group">
      <div class="form-disclaimer">${mmfgfn.msg('flag.profile.first')}</div>
    </div>
    [#--Acceptance A--]
    <ul>
      <li data-marker="a)">
        <div class="form-group">
          <div class="form-disclaimer">${mmfgfn.msg('flag.profile.second')}</div>
        </div>
        <fieldset class="form-group form-radios">
          <legend class="sr-only">${radioButtonOneSR}</legend>
          [@radioButton name=radioButtonOnePath id=radioButtonOneIdYes disabled=true value="yes" label="${mmfgfn.msg('generic.yes')}" attrs={"data-radio-btn-one-yes" : ""}/]
          [@radioButton name=radioButtonOnePath id=radioButtonOneIdNo disabled=true value="no" label="${mmfgfn.msg('generic.no')}" attrs={"data-radio-btn-one-no" : ""} /]
        </fieldset>
      </li>
      [#--Acceptance B --]
      <li data-marker="b)">
        <div class="form-group">
          <div class="form-disclaimer">${mmfgfn.msg('flag.profile.third')}</div>
        </div>
        <fieldset class="form-group form-radios">
          <legend class="sr-only">${radioButtonTwoSR}</legend>
          [@radioButton name=radioButtonTwoPath id=radioButtonTwoIdYes disabled=true value="yes" label="${mmfgfn.msg('generic.yes')}" attrs={"data-radio-btn-two-yes" : ""} /]
          [@radioButton name=radioButtonTwoPath id=radioButtonTwoIdNo disabled=true value="no" label="${mmfgfn.msg('generic.no')}" attrs={"data-radio-btn-two-no" : ""} /]
        </fieldset>
      </li>
    </ul>
    <div class="newsletter-checkbox " data-news-checkbox="">
      [@formCheckbox 
        name = "newsletterMarina"
        id="marina-news"
        inputClass = "js-wrapper-marina-rinaldi"
        label=mmfgfn.msg('register.newsletter.customer')
        value="true"
        disabled=true
        attrs={"data-signup-marina-checkbox" : ""} 
      /]
      [@formCheckbox 
        name="newsletterPersona"
        id="persona-news"
        inputClass="js-wrapper-persona"
        label=mmfgfn.msg('register.newsletter.customer.persona')
        value="true"
        disabled=true
        attrs={"data-signup-persona-checkbox" : ""} 
      /]
    </div>
  </div>

  <div class="form-reg__submit cta-form">
    [#if mmfgfn.newPrivacyFlag() ]
      [#include "./form-invisible-captcha.ftl" ]
      [@formInvisibleCaptcha formId="registerForm" label=mmfgfn.msg('register.submit') disabled=true cssClasses="--full-width js-button-submit register custom-button --primary"
      attrs={
        "data-register-submit":""
        } /]
    [#else]
      [@button
        type="submit"
        cssClass="--full-width register js-button-submit"
        disabled=true
        msg=mmfgfn.msg('register.submit')
        attrs={
        "data-register-submit":""
        }
      /]
    [/#if]
  </div>
</form>

