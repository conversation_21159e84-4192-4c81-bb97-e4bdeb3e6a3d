[#include "../ui/a.ftl"]
<div class="block__title">${mmfgfn.msg('text.account.profile.payment.option.title')}</div>
<div class="block__description">${mmfgfn.msg('text.account.profile.payment.option.description')}</div>
<div id="js-replace-paypal" class="form-box">
  <div class="block__description">${mmfgfn.msg('customer.mysocial.unsubscribe.ok')}</div>
  <form data-remote="true" action="?" method="POST">
    <div class="custom-toggle">
      <label for="account-paypal-activation">
        <input type="hidden" name="update-paypal" value="true" />
        <input
          type="checkbox"
          id="account-paypal-activation"
          class="js-ajax-toggle"
          name="usePaypal"
          ${((paymentOptions.paypal.getEnabledByUser())!false)?then("checked","")}
          data-account-paypal-activation=""
          aria-label="${mmfgfn.msg('text.account.profile.payment.option.title')}"
        />
        <span class="slider"></span>
      </label>
    </div>
  </form>
</div>
<div class="terms">
  <div class="block__description">
    [#assign termsAnchor]
    [@a_link href = mmfgfn.termsLink() isProperty=true/]
    [/#assign]
    ${mmfgfn.msg('text.account.profile.payment.option.disclaimer',termsAnchor)}
  </div>
</div>
    
