[#macro wishlistItem entry variants uniqueSize=false productCount=0 maximumItems=0 ]
  [#assign productStockLevelSize = productStocks.getStockLevel()]
  [#assign product = entry.getProduct()]
  [#assign colour = (product.getColor())]
  [#assign colourLabel = "${mmfgfn.msg('entry.variants.colour')} ${colour}"]
  [#assign productCount = productCount + 1]
  [#assign productCode = product.getCode()]
  [#assign productImagesCode = (variants?size gt 0 && product.getColorVariant() )?then(product.getCode(), product.getBaseProduct())]

  [#include "./product/product-tag-labels.ftl" ]
  [#include "./product/product-notifyme.ftl"]
  [#include "./product/product-buttons.ftl"]
  [#include "./ui/picture.ftl" ]

  <div
    class="c-product-card"
    data-vm-code="${(mmfgfn.canSort())?then(productCode, '')}"
    data-code="${productCode}"
  >
    [#assign isHtmlDataTracking=true position="${(position?? && position?has_content)?then(position, '')}" isAlgoliaEnabled = sapfn.isAlgoliaEnabled() /]
    <div class="product-card__wrapper">
      <button
        type="button"
        class="icon-close --pointer delete js-wishlist-cta js-remove-from-wish"
        data-code="${productCode}"
        data-wish-remove-url="${ctx.contextPath}/action/miniwishlist/remove?productCode=${productCode}"
        aria-label="${mmfgfn.msg('wishList.product.remove')} ${product.summary}"
      >
      </button>
      <div class="product-card__images__wrapper">
        <a
          href="${mmfgfn.producturl(product)}"
          class="product-card__images"
          data-tracking-action="productView"
         >
          [#include "/core-magnolia-module/templates/includes/analytics-product.ftl" /]
          <div class="product-card__carousel">
            [#assign thumbnail_main=mmfgfn.imageByFormat(product, 'thumbnail-a')]
            [@picture cssClasses="product-card__carousel__main" imageSrc="${thumbnail_main}" isProduct=true cardHeight='878' cardWidth='668'/]

            [#assign thumbnail_secondary=mmfgfn.imageByFormat(product, 'thumbnail-a')]
            [@picture cssClasses="product-card__carousel__secondary" imageSrc="${thumbnail_secondary}" isProduct=true cardHeight='878' cardWidth='668'/]
          </div>
        </a>
        [#-- EDITORIAL LABEL --]
        [@productTagLabels product=product /]
      </div>
      <div class="product-card__info__wrapper">
        [#if product.collectionLabel?has_content]
          <p class="product-card__collection">${product.collectionLabel}</p>
        [/#if]
        [#-- NAME --]
        <a href="${mmfgfn.producturl(product)!}" data-tracking-action="productView">
          [#include "/core-magnolia-module/templates/includes/analytics-product.ftl" /]
          <h2 class="product-card__name">${product.summary}</h2>
        </a>
        [#if mmfgfn.isShopEnabled()]
          [#include "./prices.ftl"]
          [#assign inSale = (mmfgfn.isSaleModeEnabled(mmfgfn.getSaleMode()?upper_case)) && product.getIsInSales() ]
          [@prices
            price=product.price
            salesPrice=(product.salesPrice!{})!{}
            inSale=inSale
          /]
          [#assign notifyVisible = mmfgfn.isProductUnavailable(entry.product)!false]
          <div class="product-card__selector ${notifyVisible?then('is-unavailable', '')}">
            [#include "wishlist-item-sizes.ftl"]
            [@productNotifyMe
              entry=entry
              uniquesize=uniqueSize
              productCode="${uniqueSize?then(entry.product.variantOptions[0].code, entry.product.code)}"
              isWishlist=true
              notifyVisible=notifyVisible
            /]
          </div>
          [@productButtons
            uniquesize=uniqueSize
            product=entry.product
            isWishlist=true
            notifyVisible=notifyVisible
            currentWishlist=currentWishlist
          /]
        [/#if]
      </div>
    </div>
  </div>
[/#macro]
