[#include "../ui/a.ftl" ]
[#include "../ui/input.ftl" ]

[#assign yextCategories = actpage.yextCategories?has_content?then((cmsfn.children(actpage.yextCategories!, 'mgnl:contentNode'))![], [])]

<div class="yext-faq-header ${categoryFaqsIsNotEmpty?then('', ' no-breadcrumbs')} ${(searchFaqsIsNotEmpty && searchFaqs.results?has_content)?then('--search-result', '')}">
    [#if categoryFaqsIsNotEmpty]
        [#include "yext-faq-breadcrumbs.ftl" /]
    [/#if]
    <div class="yext-faq-header__container">
        [#if categoryFaqsIsNotEmpty]
            <h1 class="yext-faq-header__title">${categoryTraslated}</h1>
            <p class="yext-faq-header__subtitle --mobile">${mmfgfn.msg("yext.header.subtitle")}</p>
        [#else]
            <p class="yext-faq-header__title">${mmfgfn.msg("yext.header.title")}</p>
            <p class="yext-faq-header__subtitle">${mmfgfn.msg("yext.header.subtitle")}</p>
        [/#if]
        <form class="yext-faq-header__form l-s__form" action="${mmfgfn.linkToSelf()}" method="GET"
            data-yext-form="" data-faq-base-url="${ctx.request.requestURL}"
            data-yext-faq-autocomplete="">
            <div class="yext-faq-header__input-container">
                <i class="icon-search bg-none -m" data-search-icon=""></i>
                [@input
                    name="q"
                    value="${ctx.getParameter('q')!?html}"
                    variant="input-search"
                    placeholder="${mmfgfn.msg('yext.header.input.placeholder')}"
                    attrs={
                        "data-yext-input":"",
                        "data-base-url":"${yextParams?has_content?then(yextParams.verticalSearchApiHost?has_content?then(yextParams.verticalSearchApiHost, '') , '')}",
                        "data-api-key":"${yextParams?has_content?then(yextParams.apiKey?has_content?then(yextParams.apiKey, '') , '')}",
                        "data-experience-key":"${yextParams?has_content?then(yextParams.experienceKey?has_content?then(yextParams.experienceKey, '') , '')}",
                        "data-vertical-key":"${yextParams?has_content?then(yextParams.verticalKeyFaq?has_content?then(yextParams.verticalKeyFaq, '') , '')}",
                        "data-search-uri":"${yextParams?has_content?then(yextParams.verticalSearchApiEndpoint?has_content?then(yextParams.verticalSearchApiEndpoint, '') , '')}",
                        "data-site-country":"${(mmfgfn.getCurrentCountryIsoCode())?lower_case}"
                    }
                /]
                <div class="yext-faq-header__icon-container">
                    <button type="submit" class="yext-faq-header__icon-button hidden" data-search-go="" aria-label="${mmfgfn.msg("notifyMe.button")}"></button>
                    <i class="icon-chevron-right -m" data-search-icon=""></i>
                    <i class="icon-search-close hidden" data-close-search=""></i>
                </div>
            </div>
        </form>
        [#if yextCategories?size gt 0 && popularFaqs?? && popularFaqs?has_content && !(ctx.getParameter('q')!?html)?has_content]
            <div class="yext-faq-header__categories">
                <div class="yext-faq-header__categories__box">
                      [#list yextCategories as yextCategory]
                          [@a_link
                              cssClass="button button--secondary"
                              href="${mmfgfn.link(yextCategory.linkCategory)}"
                          ]
                              <span class="text">${yextCategory.titleCategory}</span>
                          [/@a_link]
                      [/#list]
                </div>
            </div>
        [/#if]
    </div>
</div>