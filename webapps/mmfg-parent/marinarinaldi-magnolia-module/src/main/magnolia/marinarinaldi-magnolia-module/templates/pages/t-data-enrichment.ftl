[#include "./page.ftl" ]
[#include "../includes/ui/input.ftl" ]
[#include "../includes/ui/datepicker.ftl" ]
[#include "../includes/ui/checkbox.ftl" ]
[#include "../includes/ui/radio-button.ftl" ]
[#include "../includes/ui/a.ftl" ]
[#include "../includes/ui/select.ftl" ]
[#include "../includes/form/form-invisible-captcha.ftl" ]
[#include "../includes/ui/picture.ftl" ]

[#if content.pageImage?? && content.pageImage?has_content]
  [#assign backgroundImg=damfn.getAssetLink(content.pageImage?has_content?then(content.pageImage?starts_with("jcr:")?then(content.pageImage,"jcr:"+content.pageImage) , ''))]
[/#if]
[#if content.pageImageSmall??]
  [#assign backgroundImgMobile=damfn.getAssetLink(content.pageImageSmall?has_content?then(content.pageImageSmall?starts_with("jcr:")?then(content.pageImageSmall,"jcr:"+content.pageImageSmall) , ''))]
[#else]
  [#assign backgroundImgMobile=damfn.getAssetLink(content.pageImage?has_content?then(content.pageImage?starts_with("jcr:")?then(content.pageImage,"jcr:"+content.pageImage) , ''))]
[/#if]

[@page content=content bare=true isLogoCentered=true controller="DataEnrichmentController" mainClass="p-data-enrichment"
  showFooter=true
  patternBackgroundImage=backgroundImg!
  patternBackgroundImageMobile=backgroundImgMobile!
  synchLoadControllerCss=true
]

  [#if success?has_content && success]
    <div class="wrapper --bg-white">
      <div class="header">
        <h2 class="title">${mmfgfn.msg('data.enrichment.title')}</h2>
      </div>
      <div class="confirm">
        <p class="first">${mmfgfn.msg('data.enrichment.confirm')}</p>
        <p class="second"><!--${mmfgfn.msg('opt.out.feedback2')}--></p>
      </div>
    </div>
  [#else]
    <div class="wrapper --bg-white" data-js-component="DataEnrichmentComponent">
      <div class="header">
        <h2 class="title">${content.headerTitle?has_content?then( mmfgfn.formatText(content.headerTitle) , mmfgfn.msg('data.enrichment.title'))}</h2>
        <p class="subtitle">${content.headerSubtitle?has_content?then( mmfgfn.formatText(content.headerSubtitle) , mmfgfn.msg('data.enrichment.body'))}</p>
      </div>

        [#if deleteUser?has_content && deleteUser]
          <div class="d-e__content">
            <p class="first">${mmfgfn.msg('privacy.recall.delete.success')}</p>
            <p class="second">${mmfgfn.msg('privacy.recall.delete.success.subtitle')}</p>
          </div>
        [/#if]

      <form id="data-enrichment" method="post" commandName="genericData" action="${ctx.contextPath}/insert-data" class="form">
        <input name="param1" type="hidden" value="${param1Enrichment!genericData.param1!}" />
        <input name="param2" type="hidden" value="${param2Enrichment!genericData.param2!}" />
        <input name="param13" type="hidden" value="${param1Enrichment!genericData.param1!}" />
        <input name="registered" type="hidden" value="${((genericData.getRegistered())!false)?c}" />
        [#if isJp]
          <div class="form__inputs">
            <div class="form-input">
              [@input
                name="param5"
                disabled=true
                required=true
                value=genericData.param5!
                label=mmfgfn.msg('register.lastName')
                errors=errors!{}
              /]
            </div>
            <div class="form-input">
              [@input
                name="param3"
                disabled=true
                required=true
                value=genericData.param3!
                label=mmfgfn.msg('register.firstName')
                attrs={ "maxlength": FirstName_lenght }
                errors=errors!{}
              /]
            </div>
            <div class="form-input">
              [@input
                name="lastNameKana"
                label=mmfgfn.msg('register.lastNameKana')
                disabled=true
                attrs={ "maxlength": LastName_lenght }
                errors=errors!{}
              /]
            </div>
            <div class="form-input">
              [@datepicker
                name="param7"
                label=mmfgfn.msg('address.birthdate')
                placeholder=mmfgfn.msg('datepicker.placeholder')
                type="date"
                errors=errors!{}
                value=genericData.param7!
              /]
            </div>
            <div class="form-input">
              [@input
                label=mmfgfn.msg('address.firstNameKana')
                name="firstNameKana"
                disabled=true
                required=true
                attrs={ "maxlength": FirstName_lenght }
                errors=errors!{}
              /]
            </div>
          </div>
        [#else]
          <div class="form__inputs">
            <div class="form-input">
              [@input
                label=mmfgfn.msg('register.firstName')
                name="param3"
                disabled=disabled
                required=true
                value=genericData.param3!
                attrs={ "maxlength": FirstName_lenght }
                errors=errors!{}
              /]
            </div>
            <div class="form-input">
              [@input
                name="param5"
                disabled=disabled
                required=true
                value=genericData.param5!
                label=mmfgfn.msg('register.lastName')
                attrs={ "maxlength": LastName_lenght }
                errors=errors!{}
              /]
            </div>
            <div class="form-input">
              [@datepicker
                name="param7"
                type="date"
                value=genericData.param7!
                adultValidation=true
                errors=errors!{}
                label=mmfgfn.msg('address.birthdate')
                placeholder=mmfgfn.msg('datepicker.placeholder')
              /]
            </div>
            <div class="form-input">
              [@select
                name="param14"
                required=true
                label=mmfgfn.msg('none.residenceNation')
                attrs={"data-placeholder":""}
                errors=errors!{}
              ]
                <option></option>
                [#list countries as country]
                  <option value="${country.isocode}" [#if genericData.param14?has_content && country.isocode == genericData.param14?upper_case ] selected [/#if]>${country.name}</option>
                [/#list]
              [/@select]
            </div>
            <div class="form__phone">
              [@select name="param8" cssClass="no-placeholder" required=true errors=errors!{}]
                [#list prefixNumber as k, item]
                  [#if item?? && item?has_content]
                    <option value="${item}" [#if genericData.param8?has_content && item == genericData.param8 ] selected [/#if]>${item}</option>
                  [/#if]
                [/#list]
              [/@select]
              <div class="form-input">
                [@input
                  name="param9"
                  type="number"
                  value=genericData.param9!
                  required=true
                  label=mmfgfn.msg('profile.phoneNumber')
                  attrs={ "min":"0", "maxlength":"255"}
                  errors=errors!{}
                /]
              </div>
            </div>
          </div>
          [#if !((genericData.getRegistered())!false) ]
            <div class="form__register">
              [@formCheckbox name="param10"
                             label=mmfgfn.msg('data.enrichment.flagb2c')
                             value="true"
                             checked=(genericData?? && genericData.param10?has_content && genericData.param10 == 'true')
                             attrs={ "data-js-register":""}
                             /]
              <div class="form-input">
                <ul class="form-register">
                  <li>${mmfgfn.msg('header.register.details.text1')}</li>
                  <li>${mmfgfn.msg('header.register.details.text2')}</li>
                  <li>${mmfgfn.msg('header.register.details.text3')}</li>
                </ul>
                <div class="form-input ${(genericData?? && genericData.param10?has_content && genericData.param10 == 'true')?then('' , 'hidden')}" data-js-password="">
                  [@input
                    name="param11"
                    type="password"
                    label=mmfgfn.msg('login.password')
                    attrs={ "maxlength":"255"}
                    iconClass="icon-visibility-on --pointer --m toggle-password"
                    parentAttrs={ "data-js-component":"ShowPasswordComponent" }
                    errors=errors!{}
                  /]
                  <p id="password-hint" class="password-hint">
                    ${mmfgfn.msg('register.pwd.minimum')}
                  </p>
                </div>
              </div>
            </div>
          [/#if]
        [/#if]
        <div class="form__consents">
          [#assign privacyAnchor] [@a_link href=mmfgfn.privacyLink() cssClass="underline" isProperty=true /] [/#assign]
          [#assign inputLabel=mmfgfn.msg('register.privacy.agree', privacyAnchor)]
          [@formCheckbox name="param12" id="privacy-checkbox" label=inputLabel value="true" /]

          [#assign radioButtonOnePath = (mmfgfn.isUS())?then( 'newsletter' , 'newsletterProfiling') /]
          [#assign radioButtonOneSR = (mmfgfn.isUS())?then(mmfgfn.msg("register.notProfiled.marketing"), mmfgfn.msg("register.profiled.marketing")) /]
          [#assign radioButtonTwoPath = (mmfgfn.isUS())?then( 'newsletterProfiling' , 'newsletter') /]
          [#assign radioButtonTwoSR = (mmfgfn.isUS())?then(mmfgfn.msg("register.profiled.marketing"), mmfgfn.msg("register.notProfiled.marketing")) /]
          <div class="form__disclaimer disabled" data-js-consents="">
            <p>${mmfgfn.msg('flag.profile.first')}</p>
            <ol>
              <li>
                <p>${mmfgfn.msg('flag.profile.second')}</p>
                <fieldset class="radio-group">
                  <legend class="sr-only">${radioButtonOneSR}</legend>
                  [@radioButton name=radioButtonOnePath disabled=true value="yes" label=mmfgfn.msg('generic.yes') /]
                  [@radioButton name=radioButtonOnePath disabled=true value="no" label=mmfgfn.msg('generic.no') /]
                </fieldset>
              </li>
              <li>
                <p>${mmfgfn.msg('flag.profile.third')}</p>
                <fieldset class="radio-group">
                  <legend class="sr-only">${radioButtonTwoSR}</legend>
                  [@radioButton name=radioButtonTwoPath disabled=true value="yes" label=mmfgfn.msg('generic.yes') /]
                  [@radioButton name=radioButtonTwoPath disabled=true value="no" label=mmfgfn.msg('generic.no') /]
                </fieldset>
              </li>
            </ol>
          </div>
        </div>
        [@formInvisibleCaptcha
          formId="registerForm"
          label=mmfgfn.msg('register.submit')
          disabled=true
          cssClasses="custom-button --primary"
          attrs={ "data-js-submit":""}
        /]
      </form>
    </div>
  [/#if]
[/@page]
