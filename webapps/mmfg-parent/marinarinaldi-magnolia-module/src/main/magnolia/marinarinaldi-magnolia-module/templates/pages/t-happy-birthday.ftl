[#include "../includes/ui/checkbox.ftl" ]
[#include "../includes/ui/radio-button.ftl" ]
[#include "../includes/form/form-invisible-captcha.ftl" ]
[#include "../includes/ui/a.ftl" ]
[#include "../includes/ui/input.ftl" ]
[#include "../includes/ui/datepicker.ftl" ]

[#assign nowDate = mmfgfn.addDays(mmfgfn.now(), -1)?string['yyyy-MM-dd']]
[#assign parentPath = cmsfn.parent(content)]
[#if parentPath?contains("emme") ]
	[#assign action = mmfgfn.link("/emme/happy-birthday") ]
[#else]
	[#assign action = mmfgfn.link("/happy-birthday") ]
[/#if]

[#include "./page.ftl" ]
[@page
  content=content
  bare=true
  controller="DataEnrichmentController"
  secondController="RegisterController"
  pageClass="data-enrichment"
  patternBackgroundImage=damfn.getAssetLink(content.backgroundImage?starts_with("jcr:")?then(content.backgroundImage,"jcr:"+content.backgroundImage))
  patternBackgroundImageMobile=damfn.getAssetLink(content.backgroundImage?starts_with("jcr:")?then(content.backgroundImage,"jcr:"+content.backgroundImage))
  showFooter=true
  synchLoadControllerCss=true
]

	<div class="d-e__wrapper ${content.bgColor}">
		[#if success?has_content && success]
			<div class="d-e__boxes">
				<h2 class="d-e__title ${content.textColor!}">${content.headerTitle?has_content ?then (content.headerTitle , mmfgfn.msg('data.enrichment.title'))}</h2>
				<div class="d-e__content ${content.textColor!}">
					<p class="first">${mmfgfn.msg('data.enrichment.confirm')}</p>
					<p class="second"><!--${mmfgfn.msg('opt.out.feedback2')}--></p>
				</div>
			</div>
		[#else]
			<div class="d-e__boxes" data-js-component="RegistrationComponent">
				<h2 class="d-e__title ${content.textColor!}">${content.headerTitle?has_content ?then (content.headerTitle, mmfgfn.msg('data.enrichment.title'))}</h2>
				<div class="d-e__subtitle ${content.textColor!}">${content.headerSubtitle?has_content ?then (content.headerSubtitle, mmfgfn.msg('data.enrichment.body'))}</div>
				
				[#if deleteUser?has_content && deleteUser]
					<div class="d-e__content ${content.textColor!}">
						<p class="first">${mmfgfn.msg('privacy.recall.delete.success')}</p>
						<p class="second">${mmfgfn.msg('privacy.recall.delete.success.subtitle')}</p>
					</div>
				[/#if]
				
				<form id="data-enrichment" method="post" name="genericData" commandName="genericData" class="register js-validate-form registration__form" action="${action}" >
					<input name="param1" value="${(genericData.param1)!''}" id="param1" type="hidden" />
					<input name="param2" value="${(genericData.param2)!''}" id="param2" type="hidden" />
					<input name="param13" value="${(genericData.param13)!''}" id="param13" type="hidden" />
					<input name="param15" value="0" type="hidden" />
								
					<input name="happyBirthday" value=genericData.happyBirthday! name="happyBirthday" id="happyBirthday" type="hidden" />
					<input type="hidden" name="campaignName" value="[${mmfgfn.getCurrentBrand()}] Happy Birthday"/>
								
					[#if isJp]
						<div class="form-group">
							[@input label=mmfgfn.msg('register.lastName') maxlength=LastName_lenght name="param5" value=genericData.param5!'' disabled=true required=true /]
						</div>
						<div class="form-group">
							[@input label=mmfgfn.msg('register.firstName') maxlength=FirstName_lenght name="param3" value=genericData.param3!'' disabled=true required=true /]
						</div>
						<div class="form-group">
							[@input label=mmfgfn.msg('register.lastNameKana') maxlength=LastName_lenght name="lastNameKana" disabled=disabled /]
						</div>
						<div class="form-group">
							[@datepicker label=mmfgfn.msg('address.birthdate') name="param7" value=genericData.param7!'' adultValidation=true/]
						</div>
						<div class="form-group">
							[@input label=mmfgfn.msg('address.firstNameKana') maxlength=FirstName_lenght name="firstNameKana" disabled=true required=true/]
						</div>
					[#else]
						<div class="form-group">
							[@input label=mmfgfn.msg('register.firstName') maxlength=FirstName_lenght name="param3" value=genericData.param3!'' disabled=true  required=true/]
						</div>
						<div class="form-group">
							[@input label=mmfgfn.msg('register.lastName') maxlength=LastName_lenght name="param5" value=genericData.param5!'' disabled=true  required=true/]
						</div>
						<div class="form-group">
							[@datepicker label=mmfgfn.msg('address.birthdate') name="param7" value=genericData.param7!'' disabled=true  adultValidation=true/]
						</div>
						<div class="form-group select-wrapper">
							<select id="param14" value="${(genericData.param14)!''}" name="param14" required>
								<option selected disabled value="" label="${mmfgfn.msg('none.residenceNation')}"/>
									[#list countries as country]
									    <option value="${country.isocode}"
										[#if genericData.param14?has_content && country.isocode?upper_case == genericData.param14?upper_case ]
											 selected="selected"
										[/#if]
										>${country.name}</option>
									[/#list]
							</select>
							<span id="resNationError" class="error d-none">${mmfgfn.msg('none.residenceNation.invalid')}</span>
							<i class="icon-chevron-down"></i>
						</div>
						<div class="form-group telephone">
							<div class="prefix select-wrapper">
								<select id="param8" value="${(genericData.param8)!''}" name="param8" required>
									[#if blank?has_content]
										<option selected disabled value="">${blank}</option>
									[/#if]
									[#assign prefixes = (prefixNumber!['+39','+44'])]
									[#list prefixes as k, item]
										[#if item?? && item?has_content]
											<option value="${item}" [#if genericData.param8?has_content && item == genericData.param8 ]selected="selected"[/#if]>${item}</option>
										[/#if]
									[/#list]
								</select>
							<span class="error">
							[#-- <form:errors path="${path}" />  --]
							</span>
							<i class="icon-chevron-down"></i>
							</div>
							<div class="form-group phone-number">
								[@input label=mmfgfn.msg('profile.phoneNumber') maxlength=255 name="param9" value=genericData.param9!''/]
							</div>
						</div>
						[#if !(genericData.registered)!true]
							<div class="form-group custom-checkbox">
								[@formCheckbox name="param10" id="param10" cssClass="js-show-password-box" label=mmfgfn.msg('data.enrichment.flagb2c') value="true" /]
								<div class="form-register">
									<ul>
										<li>- ${mmfgfn.msg('header.register.details.text1')}</li>
										<li>- ${mmfgfn.msg('header.register.details.text2')}</li>
										<li>- ${mmfgfn.msg('header.register.details.text3')}</li>
									</ul>
								</div>
							</div>
							<div id="show-password-box" class="form-group password-group ${(genericData?? && genericData.param10?has_content && genericData.param10 == 'true') ?then ('' , 'd-none')}" data-js-component="ShowPasswordComponent">
								[@input label=mmfgfn.msg('login.password') type="password" maxlength=255 name="param11" value=genericData.param11!'' /]
							</div>
						[/#if]
					[/#if]
					
					<div class="form-group privacy-checkbox">
						[#if !mmfgfn.newPrivacyFlag()]
							<div class="checkbox">
								[#assign name="unProfiledMarketing" ]
								[#assign label="${mmfgfn.msg('register.newsletter.customer')}" ]
								[#assign cssclass="checkbox  custom-checkbox privacy-input" ]
								[@formCheckbox name="param10" id="param10" cssClass="js-show-password-box" label=mmfgfn.msg('data.enrichment.flagb2c') value=true /]
							</div>
						[/#if]
						[#assign privacyAnchor]
              <a data-modal="true" href="${mmfgfn.privacyLink()}.modal">
						[/#assign]
						[#if mmfgfn.newPrivacyFlag() ]
							<div class="checkbox">
								[#assign privacyAnchor]
									[@a_link href=mmfgfn.privacyLink() cssClass="underline" isProperty=true/]
								[/#assign]
								[#assign inputLabel=mmfgfn.msg('register.privacy.agree', privacyAnchor)]
								[@formCheckbox name="param12" id="privacy1" cssClass="privacy-input" label=inputLabel value="true" /]
							</div>
						[#else]
							<div class="checkbox">
								[#assign termsAnchor]
										<a data-modal="true" class="anchor-primary cta--link no--hover" href="${mmfgfn.termsLink()}.modal">
								[/#assign]
								[#assign inputLabel]
									${mmfgfn.msg('chekout.terms.and.conditions.read_1',termsAnchor, privacyAnchor)}
								[/#assign]
								[@formCheckbox name="unProfiledMarketing" label=inputLabel /]
							</div>
						[/#if]
					</div>


					[#if mmfgfn.newPrivacyFlag() ]
            [#assign radioButtonOnePath = (mmfgfn.isUS())?then( 'newsletter' , 'newsletterProfiling') /]
            [#assign radioButtonOneSR = (mmfgfn.isUS())?then(mmfgfn.msg("register.notProfiled.marketing"), mmfgfn.msg("register.profiled.marketing")) /]
            [#assign radioButtonOneIdYes = (mmfgfn.isUS())?then( 'postageyesNL' , 'postageyes') /]
            [#assign radioButtonOneIdNo = (mmfgfn.isUS())?then( 'postagenoNL' , 'postageno') /]
            [#assign radioButtonTwoPath = (mmfgfn.isUS())?then( 'newsletterProfiling' , 'newsletter') /]
            [#assign radioButtonTwoSR = (mmfgfn.isUS())?then(mmfgfn.msg("register.profiled.marketing"), mmfgfn.msg("register.notProfiled.marketing")) /]
            [#assign radioButtonTwoIdYes = (mmfgfn.isUS())?then( 'postageyes' , 'postageyesNL') /]
            [#assign radioButtonTwoIdNo = (mmfgfn.isUS())?then( 'postageno' , 'postagenoNL') /]
						<div class="form-group__fullwidth form-consents disabled" data-consents="">
							<div class="form-privacy">
								<div class="form-disclaimer">${mmfgfn.msg('flag.profile.first')}</div>
							</div>
							<ul class="form-privacy-check">
								<li data-marker="a)" >
									<div class="form-privacy">
										<div class="form-disclaimer">${mmfgfn.msg('flag.profile.second')}</div>
									</div>
									<fieldset class="form-privacy form-radio">
									  <legend class="sr-only">${radioButtonOneSR}</legend>
										[@radioButton
										  name=radioButtonOnePath
										  id=radioButtonOneIdYes
										  label=mmfgfn.msg('generic.yes')
										  disabled=true
										  value="yes"
                    /]
										[@radioButton
										  name=radioButtonOnePath
										  id=radioButtonOneIdNo
										  label=mmfgfn.msg('generic.no')
										  disabled=true
										  value="no"
                    /]
									</fieldset>
								</li>
								<li data-marker="b)" >
									<div class="form-privacy">
										<div class="form-disclaimer">${mmfgfn.msg('flag.profile.third')}</div>
									</div>
									<fieldset class="form-privacy form-radio">
									  <legend class="sr-only">${radioButtonTwoSR}</legend>
                    [@radioButton
                      name=radioButtonTwoPath
                      id=radioButtonTwoIdYes
                      label=mmfgfn.msg('generic.yes')
                      disabled=true
                      value="yes"
                    /]
                    [@radioButton
                      name=radioButtonTwoPath
                      id=radioButtonTwoIdNo
                      label=mmfgfn.msg('generic.no')
                      disabled=true
                      value="no"
                    /]
										<div class="privacy-alert hidden">
											${mmfgfn.msg('privacy.flag.one.invalid')}
										</div>
									</fieldset>
								</li>
							</ul>
						</div>
					[/#if]
					
					<div class="form-group__fullwidth cta-form">
						[@formInvisibleCaptcha formId="registerForm" label=mmfgfn.msg('register.submit') disabled=true cssClasses="button button--primary button--full-width create-account register js-registration-anchor"/]
						
					</div>
				</form>
							
				<script>
					document.getElementById("data-enrichment").addEventListener("submit", function(e){
						if(isEmpty(document.getElementById("param14").value)){
							e.preventDefault();
							document.getElementById("resNationError").className = "error";
							setTimeout(function(){
								const confirmButton = document.querySelectorAll('button.cta.cta-primary.js-button-submit.d-e__account-btn.loading-cta')[0];
								confirmButton.classList.remove('loading-cta');
								confirmButton.disabled = false;
							}, 1000);
						}
						else{
							document.getElementById("resNationError").className = "error d-none";
						}
					});
					function isEmpty(str) {
						return (!str || str.length === 0 );
					}
				</script>
			
			</div>
		[/#if]
	</div>
[/@page]
