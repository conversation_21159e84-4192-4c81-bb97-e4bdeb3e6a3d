[#if categoriesResultsIsNotEmpty]
    <div class="yext-faq-cards-categories cards_${categories?size}">
        [#list categoryList.results as category]
            <div class="yext-faq-cards-categories__singlecard" data-single-card="">
                <a href="${ctx.request.requestURL}/${category.url?has_content?then(category.url,'')}"
                   data-single-card-link=""
                >
                    <img src="${category.imageUrl!""}" />
                    <span class="yext-faq-cards-categories__singlecard__title">
                        ${category.title}
                    </span>
                    <span class="yext-faq-cards-categories__singlecard__text">
                        ${category.totalFaqs} ${mmfgfn.msg('yext.faq.categories.resources')}
                    </span>
                </a>
            </div>
        [/#list]
    </div>
[/#if]

<div class="yext-faq-contactus">
    [#if actpage.ctaText?? && actpage.ctaText?has_content]
        <p class="yext-faq-contactus__title">${actpage.ctaText}</p>
    [/#if]
    [#if actpage.ctaLabel?? && actpage.ctaLabel?has_content && actpage.ctaLink?? && actpage.ctaLink?has_content]
        <div class="yext-faq-contactus__cta">
            [@a_link
                href="${mmfgfn.link(actpage.ctaLink)}"
                cssClass="cta-secondary cta-full-width"
            ]
                <span class="text" data-cta-text="${actpage.ctaLabel}">${actpage.ctaLabel}</span>
            [/@a_link]
        </div>
    [/#if]
</div>