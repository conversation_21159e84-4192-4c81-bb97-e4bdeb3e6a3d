[#macro yextFaqList faqs faqLimit=0 pager=false cssClasses='' ]

    [#if faqs?? && faqs?has_content && faqs.results?? && faqs.results?has_content]
        [#assign limit = (faqLimit gt 0)?then(faqLimit, faqs.results?size)]
        <div class="yext-faq-list ${cssClasses!}" data-js-component="CollapseComponent">
            [#assign count = 0]
            [#list faqs.results as elem]
                [#assign count = count + 1]
                [#if count gt limit]
                    [#break]
                [/#if]
                <div class="yext-faq-list__question"
                    data-yext-single-faq=""
                    data-faq-url="${ctx.request.requestURL}/${elem.url}-${(elem.question?replace(' ', '-'))?lower_case}"
                    data-faq-id="${elem.id}"
                    data-category-name="${elem.name}">
                    <div class="yext-faq-list__question__title" data-trigger="">
                        <h2>${elem.question}</h2>
                        <div class="yext-faq-list__question__title__copy-link" data-copy-button="">
                            <i class="icon-copy-link"></i>
                            <span class="label-copy">${mmfgfn.msg("yext.faq.copy")}</span>
                            <span class="label-copied">${mmfgfn.msg("yext.faq.copied")}</span>
                        </div>
                        <i class="icon-chevron-up"></i>
                    </div>
                    <div class="yext-faq-list__question__answer" data-tab="">
                        <div class="yext-faq-list__question__answer__text">${elem.answer}</div>
                        [#if searchFaqsIsNotEmpty]
                          <div class="yext-faq-list__question__answer__category"><span>${mmfgfn.msg("category.subject")}:</span> ${elem.category}</div>
                        [/#if]
                    </div>
                </div>
            [/#list]
        </div>

        [#if (pager!false) ]
            [#include "yext-faq-list-pager.ftl" /]
        [/#if]
    [/#if]
[/#macro]