[#macro productPrices
  freeLabel= ''
  textAfterPrices= ''
  price= {}
  salesPrice= {}
  negative= false
  cssClass= ''
  fullPriceCssClass= ''
  salePriceCssClass= ''
  normalPriceCssClass= ''
  isSpecialPrice= false
]

[#assign inSale = (mmfgfn.isSaleModeEnabled(mmfgfn.getSaleMode()) || isSpecialPrice) && (salesPrice?has_content && salesPrice.value?has_content)]
[#assign originalFormattedPrice = price.formattedValue!'']
[#assign currentPrice = inSale?then(salesPrice.value!0, price.value!0)]
[#assign currentFormattedPrice = "${(currentPrice <= 0 && (freeLabel?? && freeLabel?has_content))?then(mmfgfn.msg(freeLabel), (inSale?then (salesPrice.formattedValue , price.formattedValue!'')))}" ]
[#assign currency = sapfn.getCurrentCurrency()!'']
[#assign currencySymbol = (currency?? && currency?has_content)?then(currency.symbol, '')]

<div class="${cssClass} ${inSale?then('sale', '')}">
  [#if inSale]
    [#-- Full Price --]
    <span class="${fullPriceCssClass} --full" data-price="${originalFormattedPrice}" aria-hidden="true">
      ${originalFormattedPrice}
    </span>
    <span class="sr-only">${mmfgfn.msg('original.price')} ${originalFormattedPrice}</span>
    [#-- Sale Price --]
    <span class="${salePriceCssClass} --discount" data-sale-price="${currentFormattedPrice}" aria-hidden="true">
      ${currentFormattedPrice}
    </span>
    <span class="sr-only">${mmfgfn.msg('current.price')} ${currentFormattedPrice}</span>
  [#else]
    [#-- Full Price --]
    <span class="${normalPriceCssClass!''} --normal" data-price="${currentFormattedPrice}" aria-hidden="true">
      ${currentFormattedPrice}
    </span>
    <span class="sr-only">${mmfgfn.msg('current.price')} ${currentFormattedPrice}</span>
  [/#if]
</div>
[/#macro]
