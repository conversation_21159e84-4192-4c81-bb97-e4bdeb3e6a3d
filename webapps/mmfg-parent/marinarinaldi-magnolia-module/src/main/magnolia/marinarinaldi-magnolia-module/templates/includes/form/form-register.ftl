[#include "../ui/a.ftl"]
[#include "../ui/input.ftl"]
[#include "../ui/checkbox.ftl"]
[#include "../ui/radio-button.ftl"]
[#include "./form-invisible-captcha.ftl"]

<div class="c-registration-form" data-js-component="RegistrationComponent">
  <form method="POST" id="registerForm" commandName="registerForm" class="register__form js-validate-form js-register-form" action="${ctx.contextPath}/register">
    <input type="hidden" name="register" value="true"/>
    <input type="hidden" name="formId" value="registerForm"/>
    <div class="register__inputs__wrapper">
      [#if !(checkout!false)]
        <h3>${mmfgfn.msg('register.new.customer')}</h3>
        <p>${mmfgfn.msg('login.subtitle')}</p>
      [/#if]
      <div class="register__inputs">
        [#if !(checkout!false)]
          <div class="firstname-container">
            [@input
              name="firstName"
              id="firstName"
              label=mmfgfn.msg('register.firstName')
              value="${registerForm.firstName!''}"
              required=true
              errors=errors!{}
              attrs={
              "maxlength":"${FirstName_lenght!''}"
              }
            /]
          </div>
          <div class="lastname-container">
            [@input
              name="lastName"
              id="lastName"
              label=mmfgfn.msg('register.lastName')
              value="${registerForm.lastName!''}"
              required=true
              errors=errors!{}
              attrs={
              "maxlength":"${LastName_lenght!''}"
              }
            /]
          </div>
        [#else]
          <input type="hidden" id="uid" name="uid" value="${(registerForm.uid)!''}"/>
          <input type="hidden" id="anonymousCheckOut" name="anonymousChekOut" value="${isAnonymousCheckout?c}"/>
          <input type="hidden" id="firstName" name="firstName" value="${(registerForm.firstName)!''}"/>
          <input type="hidden" id="lastName" name="lastName" value="${(registerForm.lastName)!''}"/>
          <input type="hidden" id="orderNumber" name="orderNumber" value="${orderNumber!}"/>
        [/#if]
          <div class="email-container">
            [@input
              name="email"
              id="email"
              label=mmfgfn.msg('address.email')
              value="${registerForm.email!''}"
              type="email"
              required=true
              errors=errors!{}
              attrs={
              "maxlength":"${EmailAdd_lenght!''}"
              }
            /]
          </div>
          <div class="password-container">
            [@input
              name="password"
              id="password"
              label=mmfgfn.msg('register.pwd')
              value="${registerForm.password!''}"
              type="password"
              required=true
              errors=errors!{}
              iconClass="icon-visibility-on --pointer --m toggle-password"
              attrs={"maxlength":"50"}
              parentAttrs={"data-js-component":"ShowPasswordComponent"}
            /]
            [#if !(checkout!false)]
              <div class="form-login__psw-reset --mini --txt-black">
                [@a_link
                  href="${(inHeader?has_content && inHeader)?then( 'javascript:void(0)' , mmfgfn.link('/passwordreset') )}"
                  attrs={"data-pwd-recovery-btn":""}
                ]
                  ${mmfgfn.msg('login.link.forgottenPwd')}
                [/@a_link]
              </div>
            [/#if]
          </div>
          
          [#if !mmfgfn.newPrivacyFlag()]
            [@formCheckbox
              name="unProfiledMarketing"
              label=mmfgfn.msg('register.newsletter.customer')
              cssClass="checkbox custom-checkbox privacy-input"
            /]
          [/#if]
          [#assign privacyAnchor]
            [@a_link href=mmfgfn.privacyLink() cssClass="anchor-primary cta--link underline" isProperty=true /]
          [/#assign]
          [#assign inputLabel = mmfgfn.msg('register.privacy.agree', privacyAnchor) /]
          [#if mmfgfn.newPrivacyFlag()]
            [@formCheckbox name = "privacy"
              id="privacy1"
              label=inputLabel
              value="true"
              attrs={"data-privacy-checkbox" : ""}
            /]
          [#else]
            [@formCheckbox name = "privacy"
              id="privacy1"
              label=inputLabel
              value="true"
              attrs={"data-privacy-checkbox" : ""}
            /]
          [/#if]
      </div>
    </div>

    [#if mmfgfn.newPrivacyFlag()]
      [#assign radioButtonOnePath = (mmfgfn.isUS())?then('unProfiledMarketing', 'profiledMarketing') /]
      [#assign radioButtonOneSR = (mmfgfn.isUS())?then(mmfgfn.msg("register.notProfiled.marketing"), mmfgfn.msg("register.profiled.marketing")) /]
      [#assign radioButtonOneIdYes = (mmfgfn.isUS())?then('postageyesNL', 'postageyes') /]
      [#assign radioButtonOneIdNo = (mmfgfn.isUS())?then('postagenoNL', 'postageno') /]
      [#assign radioButtonTwoPath = (mmfgfn.isUS())?then('profiledMarketing', 'unProfiledMarketing') /]
      [#assign radioButtonTwoSR = (mmfgfn.isUS())?then(mmfgfn.msg("register.profiled.marketing"), mmfgfn.msg("register.notProfiled.marketing")) /]
      [#assign radioButtonTwoIdYes = (mmfgfn.isUS())?then('postageyes', 'postageyesNL') /]
      [#assign radioButtonTwoIdNo = (mmfgfn.isUS())?then('postageno', 'postagenoNL') /]
      <div class="form-consents disabled" data-consents="">
        <p>${mmfgfn.msg('flag.profile.first')}</p>
        <ul>
          <li data-marker="a)">
            <p>${mmfgfn.msg('flag.profile.second')}</p>
            <fieldset class="form-radios">
              <legend class="sr-only">${radioButtonOneSR}</legend>
              [@radioButton name=radioButtonOnePath id=radioButtonOneIdYes disabled=true value="yes" label="${mmfgfn.msg('generic.yes')}" attrs={"data-radio-btn-one-yes" : ""} /]
              [@radioButton name=radioButtonOnePath id=radioButtonOneIdNo disabled=true value="no" label="${mmfgfn.msg('generic.no')}" attrs={"data-radio-btn-one-no" : ""} /]
            </fieldset>
          </li>
          <li data-marker="b)">
            <p>${mmfgfn.msg('flag.profile.third')}</p>
            <fieldset class="form-radios">
              <legend class="sr-only">${radioButtonTwoSR}</legend>
              [@radioButton name=radioButtonTwoPath id=radioButtonTwoIdYes disabled=true value="yes" label="${mmfgfn.msg('generic.yes')}" attrs={"data-radio-btn-two-yes" : ""} /]
              [@radioButton name=radioButtonTwoPath id=radioButtonTwoIdNo disabled=true value="no" label="${mmfgfn.msg('generic.no')}" attrs={"data-radio-btn-two-no" : ""} /]
            </fieldset>

              [#--<div class="privacy-alert hidden">
                  ${mmfgfn.msg('privacy.flag.one.invalid')}
              </div>--]

          </li>
        </ul>
        <div class="newsletter-checkbox " data-news-checkbox="">
          [@formCheckbox 
            name = "newsletterMarina"
            id="marina-news"
            inputClass = "js-wrapper-marina-rinaldi"
            label=mmfgfn.msg('register.newsletter.customer')
            value="true"
            disabled=true
            attrs={"data-signup-marina-checkbox" : ""} 
          /]
          [@formCheckbox 
            name="newsletterPersona"
            id="persona-news"
            inputClass="js-wrapper-persona"
            label=mmfgfn.msg('register.newsletter.customer.persona')
            value="true"
            disabled=true
            attrs={"data-signup-persona-checkbox" : ""} 
          /]
        </div>
      </div>
    [/#if]
    <div class="form-submit">
      [@formInvisibleCaptcha
        formId="registerForm"
        disabled=true
        cssClasses="custom-button --primary --full-width"
        label="${mmfgfn.msg('register.submit')}"
        attrs={
        "data-register-submit":""
        }
      /]
    </div>
  </form>
</div>