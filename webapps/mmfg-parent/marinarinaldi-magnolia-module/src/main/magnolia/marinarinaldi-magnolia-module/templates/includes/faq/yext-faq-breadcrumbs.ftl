<div class="yext-faq-breadcrumbs breadcrumbs__wrapper">
    <a href="${mmfgfn.link('/home')}">${mmfgfn.msg("breadcrumb.home")}</a>
    <span class="icon-chevron-right --s"></span>
    <a href="${mmfgfn.link(actpage)!}">${mmfgfn.msg("yext.breadcrumb.page.title")}</a>
    <span class="icon-chevron-right --s --back"></span>
    <a href="javascript:void(0)" class="last-button">${categoryTraslated}</a>
    <a href="${mmfgfn.link(actpage)!}" class="back-button --back">${mmfgfn.msg("error.back")}</a>
    [#-- SEO JSON --]
    [#if actpage?has_content]
        <script type="application/ld+json">
            {
                "@context": "https://schema.org",
                "@type": "BreadcrumbList",
                "itemListElement":
                [
                    {
                        "@type": "ListItem",
                        "position": 1,
                        "item":
                        {
                            "@id": "${mmfgfn.baseurl()!}",
                            "name": "Homepage"
                        }
                    },
                    {
                        "@type": "ListItem",
                        "position": 2,
                        "item":
                        {
                            "@id": "${mmfgfn.baseurl()!}${mmfgfn.link(actpage)!}",
                            "name": "${mmfgfn.msg('yext.breadcrumb.page.title')}"
                        }
                    },
                    {
                        "@type": "ListItem",
                        "position": 3,
                        "item":
                        {
                            "@id": "${mmfgfn.baseurl()!}${mmfgfn.link(actpage)!}/${categoryName}",
                            "name": "${categoryName}"
                        }
                    }
                ]
            }
        </script>
    [/#if]
</div>