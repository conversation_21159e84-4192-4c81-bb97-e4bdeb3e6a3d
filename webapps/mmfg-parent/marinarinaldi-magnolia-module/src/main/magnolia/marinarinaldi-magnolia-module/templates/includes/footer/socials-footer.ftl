[#include "../ui/a.ftl"]

[#assign contentPath = cmsfn.contentByPath(mmfgfn.getOverrideSitePath("home"))! ]

[#if (contentPath?? && contentPath?has_content) ]
  [#assign currentNode = cmsfn.asJCRNode(contentPath.marinaRinaldiSocials)!]
  [#if currentNode?? && currentNode?has_content]
    [#assign socials = (cmsfn.asContentMapList(cmsfn.children(currentNode, "mgnl:contentNode")))!]
    [#if (socials?? && socials?has_content) && (socials?size >= 0) && ((content.title!'') != "Country Selection")]
      <div class="footer__social">
        <h3>${contentPath.marinaSocialsTitle}</h3>
        <div class="footer__social-bar">
          [#list socials as social]
            [@a_link
              href="${social.href!}"
              external=true
              iconClass="icon-${social.icon!}"
              attrs= {
                "aria-label":"${social.title!'social'} link"
              }
            /]
          [/#list]
        </div>
      </div>
    [/#if]
  [/#if]
  [#assign currentNode = cmsfn.asJCRNode(contentPath.personaSocials)!]
  [#if currentNode?? && currentNode?has_content]
    [#assign socials = (cmsfn.asContentMapList(cmsfn.children(currentNode, "mgnl:contentNode")))!]
    [#if (socials?? && socials?has_content) && (socials?size >= 0) && ((content.title!'') != "Country Selection")]
      <div class="footer__social">
        <h3>${contentPath.personaSocialsTitle}</h3>
        <div class="footer__social-bar">
          [#list socials as social]
            [@a_link
              href="${social.href!}"
              external=true
              iconClass="icon-${social.icon!}"
              attrs= {
                "aria-label":"${social.title!'social'} link"
              }
            /]
          [/#list]
        </div>
      </div>
    [/#if]
  [/#if]
[/#if]
