import Component from '../abstracts/Component';
import {isMobile, MobileBreakpoint} from '../utils/MobileUtils';
import YextFaqUtils from '../utils/YextFaqUtils';

export default class FaqComponent extends Component {
  get SELECTORS() {
    return {
      yextForm: '[data-yext-form]',
      yextInput: '[data-yext-input]',
      yextFaqContainer: '[data-yext-faq-autocomplete]',
      yextSingleFaq: '[data-yext-single-faq]',
      yextFaqCopyLink: '[data-copy-button]',
      closeSearchBtn: '[data-close-search]',
      performSearchBtn: '[data-search-go]',
      performSearchIcon: '[data-search-icon]',
      singleCard: '[data-single-card]',
      singleCardLink: '[data-single-card-link]',
      collapseTrigger: '[data-trigger]',
      mainHeader: '[data-sticky-header]'
    };
  }

  get CLASSES() {
    return {
      active: 'active',
      hidden: '--hidden',
      copied: 'copied',
      faqsContainer: 'yext-faq-suggestions',
      singleFaq: 'yext-faq-suggestions__singlefaq',
      singleFaqTitle: 'yext-faq-suggestions__singlefaq__title',
      singleFaqCat: 'yext-faq-suggestions__singlefaq__category'
    };
  }

  constructor(htmlEl) {
    super(htmlEl);
    this.clickOutside = this.clickOutside.bind(this);
    this.yextForm = this.$component.querySelector(this.SELECTORS.yextForm);
    this.yextInput = this.$component.querySelector(this.SELECTORS.yextInput);
    this.yextFaqContainer = this.$component.querySelector(this.SELECTORS.yextFaqContainer);
    this.yextSingleFaq = this.$component.querySelectorAll(this.SELECTORS.yextSingleFaq);
    this.yextFaqCopyLink = this.$component.querySelectorAll(this.SELECTORS.yextFaqCopyLink);
    this.closeSearchBtn = this.$component.querySelector(this.SELECTORS.closeSearchBtn);
    this.performSearchBtn = this.$component.querySelector(this.SELECTORS.performSearchBtn);
    this.performSearchIcon = this.$component.querySelector(this.SELECTORS.performSearchIcon);
    this.singleCard = this.$component.querySelectorAll(this.SELECTORS.singleCard);
  }

  buildFaqs(event) {
    // faqs
    let faqs = event.payload.faqs.results;
    // check
    if (typeof faqs === 'undefined') {
      console.log('no faq founded');
      return false;
    }
    // limit for first void search
    if (event.payload.input === '') {
      faqs = faqs.slice(0,5);
    }
    // vars
    let searchTerms = event.payload.input.split(' ');
    let singleFaqBasqUrl = this.yextForm.dataset;
    // outer div
    let faqDiv = document.createElement('div');
    faqDiv.classList.add(this.CLASSES.faqsContainer);
    // cycle each results
    faqs.forEach( faq => {
      //
      let catName = faq.data.c_linked_entity_FAQ[0].name;
      let catTitle = faq.data.c_linked_entity_FAQ[0].title;
      // single faq build
      let singleFaqDiv = document.createElement('div');
      singleFaqDiv.classList.add(this.CLASSES.singleFaq);
      // title
      let singleFaqTitle = document.createElement('span');
      singleFaqTitle.classList.add(this.CLASSES.singleFaqTitle);
      singleFaqTitle.innerHTML = faq.data.question;
      // build title with highlighted words
      const escapedTerms = searchTerms.map(str => str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'));
      const searchRegex = new RegExp(escapedTerms.join('|'), 'gi');
      const highlightedTitle = faq.data.question.replace(searchRegex, (match) => `<b>${match}</b>`);
      singleFaqTitle.innerHTML = highlightedTitle;
      singleFaqDiv.append(singleFaqTitle);
      let hastTitle = encodeURI(faq.data.question.replace(/\s/g,'-').toLowerCase());
      // cat
      let singleFaqCat = document.createElement('span');
      singleFaqCat.classList.add(this.CLASSES.singleFaqCat);
      singleFaqCat.innerHTML = catTitle.charAt(0).toUpperCase() + catTitle.slice(1);
      singleFaqDiv.append(singleFaqCat);
      // url
      let singleFaqUrl = singleFaqBasqUrl.faqBaseUrl+'/'+catName+'#f-'+faq.data.id+'-'+hastTitle;
      singleFaqDiv.setAttribute('data-url', singleFaqUrl);
      singleFaqDiv.setAttribute('data-cat', catName);
      singleFaqDiv.setAttribute('data-faq-id', faq.data.id);
      // final append
      faqDiv.append(singleFaqDiv);
    });
    // append to input or rebuild
    let faqWindow = this.yextFaqContainer.querySelector('.'+this.CLASSES.faqsContainer);
    if (faqWindow == null) {
      this.yextFaqContainer.append(faqDiv);
    } else {
      faqWindow.innerHTML = faqDiv.innerHTML;
    }
    // then bind click search TODO
    this.bindSearchOnFaq();
  }

  clickOutside(e) {
    // if I click outside the wrapper
    if (e.target !== this.yextFaqContainer && e.target.closest('.'+this.CLASSES.faqsContainer) === null) {
      this.closeFaqsWindow();
    }
  }

  closeFaqsWindow() {
    let faqWindow = this.yextFaqContainer.querySelector('.'+this.CLASSES.faqsContainer);
    if (faqWindow !== null) {
      faqWindow.remove();
    }
  }

  debounce(callback, wait) {
    let timeout;
    return (...args) => {
      this.toggleIcon();
      clearTimeout(timeout);
      timeout = setTimeout(function() { callback.apply(this, args); }, wait);
    };
  }

  eventAutocomplete() {
    if (this.yextInput.value.length > 2) {
      // launch the autocomplete
      YextFaqUtils.getYextFaq(this.yextInput.value);
    }
    if (this.yextInput.value.length) {
      // void container ?
      let faqWindow = this.yextFaqContainer.querySelector('.'+this.CLASSES.faqsContainer);
      if (faqWindow !== null) {
        faqWindow.innerHTML = '';
      }
    }
  }

  bindSearch() {
    let self = this;
    if (this.yextInput == null) return false;
    // on input, start the countdown
    this.yextInput.addEventListener('input', self.debounce( () => {
      self.eventAutocomplete();
    }, 500));
  }

  bindSearchOnFaq() {
    let self = this;
    let faqWindow = this.yextFaqContainer.querySelector('.'+this.CLASSES.faqsContainer);
    // check faq window opened
    if (faqWindow !== null) {
      let faqs = faqWindow.querySelectorAll('.'+this.CLASSES.singleFaq);
      if (faqs !== null && faqs.length > 0) {
        // let's cycle all the faq
        faqs.forEach( singleFaq => {
          // bind click
          singleFaq.addEventListener('click', () => {
            if (typeof singleFaq.dataset.url !== 'undefined' && singleFaq.dataset.url !== '') {
              // check path if is the same -> I have to reload the page
              let beforePath = window.location.pathname;
              location.replace(singleFaq.dataset.url);
              // check if I am in the same page
              if (window.location.pathname.includes(singleFaq.dataset.cat)) {
                self.closeFaqsWindow();
                let targetFaq = document.querySelector('[data-faq-id='+singleFaq.dataset.faqId+']');
                setTimeout(function() {
                  self.faqOpen(targetFaq);
                }, 500);
              }
            }
          });
        });
      }
    }
  }

  getHeaderHeight() {
    // selectors
    let mainHeader = document.querySelector(this.SELECTORS.mainHeader);
    // offset
    let mainHeaderOffset = mainHeader ? mainHeader.offsetHeight : 0;
    return mainHeaderOffset;
  }

  faqOpen(singleFaq) {
    let self = this;
    if (singleFaq === null) return false;
    let trigger = singleFaq.querySelector(this.SELECTORS.collapseTrigger);
    if (trigger !== null) {
      trigger.click();
    }
    setTimeout(function() {
      let scrollFromTop = window.pageYOffset + singleFaq.getBoundingClientRect().top - self.getHeaderHeight();
      window.scroll({
        behavior: 'smooth',
        left: 0,
        top: scrollFromTop
      });
    }, 500);
  }

  singleFaqOpen() {
    let self = this;
    // check hash
    let hash = location.hash.substr(1);
    if (hash !== '' && this.yextSingleFaq !== null && this.yextSingleFaq.length > 0 ) {
      // let's cycle all the faq
      this.yextSingleFaq.forEach( singleFaq => {
        if (hash.includes(singleFaq.dataset.faqId)) {
          // let's open the faq
          self.faqOpen(singleFaq);
        }
      });
    }
  }

  singleFaqLinkCopy() {
    let self = this;
    // loop the links
    if (this.yextFaqCopyLink !== null && this.yextFaqCopyLink.length > 0) {
      // let's cycle all the faq
      this.yextFaqCopyLink.forEach( copyLink => {
        copyLink.addEventListener('click', (e) => {
          e.stopPropagation();
          let singleFaq = copyLink.closest(self.SELECTORS.yextSingleFaq);
          self.writeClipboardText(copyLink,singleFaq.dataset.faqUrl);
        });
      });
    }
  }

  toggleIcon() {
    if (this.yextInput.value !== '') {
      this.performSearchBtn.classList.remove(this.CLASSES.hidden);
      this.performSearchIcon.classList.remove(this.CLASSES.hidden);
      $(this.SELECTORS.performSearchIcon).addClass(this.CLASSES.active);
    } else {
      this.performSearchBtn.classList.add(this.CLASSES.hidden);
      this.performSearchIcon.classList.add(this.CLASSES.hidden);
      $(this.SELECTORS.performSearchIcon).removeClass(this.CLASSES.active);
    }
    this.closeSearchBtn.classList.add(this.CLASSES.hidden);
  }

  writeClipboardText(btn, text) {
    let self = this;
    try {
      navigator.clipboard.writeText(text).then(function() {
        btn.classList.add(self.CLASSES.copied);
        setTimeout(function() {
          btn.classList.remove(self.CLASSES.copied);
        }, 2000);
      }, function() {
        /* clipboard write failed */
        console.log('NOT copied');
      });
    } catch(error) {
      console.error(error.message);
    }
  }

  bindEvents() {
    let self = this;
    // click on the close icon
    this.closeSearchBtn.addEventListener('click', () => {
      this.yextInput.value = '';
      this.closeSearchBtn.classList.add(this.CLASSES.hidden);
      this.yextInput.focus();
    });
    // click on input
    this.yextInput.addEventListener('focus', (e) => {
      // launch void suggestion
      YextFaqUtils.getYextFaq('');
    });
    // let's listen for faq to arrive
    this.$on(this.$customEvents.YEXT_EVENTS.faqsLoaded, (event) => {
      // go build faqs
      self.buildFaqs(event);
    });
    // click on dom
    document.addEventListener('click', this.clickOutside);
  }

  bindFooterCardActive() {
    let currentUrl = window.location.origin + window.location.pathname;
    this.singleCard.forEach(singleCard => {
      let link = singleCard.querySelector(this.SELECTORS.singleCardLink);
      if (link.href === currentUrl) {
          singleCard.classList.add(this.CLASSES.active);
      }
    });
  }

  render() {
    super.render();
    this.bindSearch();
    this.bindEvents();
    this.singleFaqOpen();
    this.singleFaqLinkCopy();
    this.bindFooterCardActive();
  }
}
