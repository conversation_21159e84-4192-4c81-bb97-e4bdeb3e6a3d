.yext-faq {
  &-breadcrumbs {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 5px;
    margin-top: 24px;
    padding: 8px 24px;
    a {
      @include body-xs;
      color: palette(dark-grey);
      text-decoration: none;
      &.last-button {
        @include body-xs--medium;
        color: palette(black);
        border-bottom: none;
        pointer-events: none;
        cursor: unset;
      }
    }
    span {
      color: palette(dark-grey);
    }
  }
  &-header {
    background-color: palette(ultra-light-grey);
    position: relative;
    z-index: 1;
    &__container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    &__categories {
      height: 100%;
      max-width: 100%;
      display: flex;
      align-items: center;
      margin-top: 32px;
      &__box {
        display: flex;
        overflow-x: auto;
        justify-content: left;
        align-items: center;
        scrollbar-width: none;
        gap: 32px;
        a {
          @include body-s;
          text-transform: uppercase;
          flex: 0 0 auto;
          width: auto;
          line-height: normal;
          text-decoration: none;
          &:first-child {
            margin-left: 8px;
          }
          &:last-child {
            margin-right: 8px;
          }
          &:hover {
            font-weight: $font-500;
            text-decoration: underline;
            text-underline-offset: 2px;
          }
        }
      }
    }
    &__form {
      width: 100%;
      display: flex;
      justify-content: center;
      position: relative;
    }
    &__icon-container {
      button {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        position: absolute;
        right: 0;
        display: block;
        background-color: transparent;
        border: none;
        width: 24px;
        height: 32px;
        cursor: pointer;
        padding: 0;
      }
      i {
        font-size: 2.4rem;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 32px;
        width: 32px;
        color: palette(light-grey);
        border: 1px solid palette(light-grey);
        border-radius: 50%;
        &:before {
          width: 22px;
          height: 24px;
        }
        &.active {
          background-color: palette(white);
          color: palette(black);
          border: 1px solid palette(processing);
        }
        &:hover {
          cursor: pointer;
        }
      }
    }
    &__input-container {
      position: relative;
      max-height: 48px;
      border-bottom: 1px solid palette(black);
      column-gap: 10px;
      padding: 12px 16px;
      box-shadow: none;
      display: flex;
      justify-content: space-between;
      align-items: center;
      >i {
        position: initial;
        font-size: 24px;
        width: 24px;
        height: 24px;
        &.bg-none:before {
            background-color: transparent;
        }
      }
      .custom-input {
        flex: 1 1 auto;
        height: auto;
        &.base{
          border-bottom: none;
          &:has(input:is(:placeholder-shown)) {
            border-bottom: none;
          }
        }
        input {
          @include body-s;
          color: palette(black);
          padding: 0;
          border: none;
          &::placeholder {
            @include body-s;
            color: palette(medium-grey);
          }
        }
      }
    }
    &__subtitle {
      @include body-l;
      color: palette(dark-grey);
      margin-bottom: 40px;
    }
    &__title {
      @include h5;
      padding-top: 10px;
      margin-bottom: 16px;
    }

  }
}

@media #{$until-medium} {
  .yext-faq {
    &-breadcrumbs {
      margin-bottom: 32px;
      >*:not(.--back) {
        display: none;
      }
      span {
        &.--back {
          -webkit-transform: scaleX(-1);
          transform: scaleX(-1);
        }
      }
    }
    &-header {
      padding-bottom: 24px;
      &.no-breadcrumbs {
        padding-bottom: 24px;
        padding-top: 24px;
      }
      &:not(.no-breadcrumbs), &.--search-result {
        border-bottom: none;
      }
      &__input-container {
        width: 100%;
        input {
          font-size: 16px;
          &::placeholder {
            font-size: 16px;
          }
        }
      }
      &__form,
      &__subtitle,
      &__title {
        padding-right: 8px;
        padding-left: 8px;
      }
    }
  }
}

@media #{$from-medium} {
  .yext-faq {
    &-breadcrumbs {
      .back-button {
        display: none;
      }
    }
    &-header {
      padding-bottom: 32px;
      &.no-breadcrumbs {
        padding-top: 32px;
      }
      &__input-container {
        width: 100%;
      }
      &__categories {
        &__box {
          a {
            &:first-child {
              margin-left: 0;
            }
            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
    }
  }
}

@media #{$from-large} {
  .yext-faq {
    &-breadcrumbs {
      padding-right: 32px;
    }
  }
}
