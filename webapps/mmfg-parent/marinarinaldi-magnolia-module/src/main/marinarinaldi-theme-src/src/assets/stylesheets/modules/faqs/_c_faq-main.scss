.yext-faq {
  &-list {
    &-pager {
      width: 100%;
      display: flex;
      justify-content: center;
      >a {
        text-decoration: none;
        &.disabled {
          pointer-events: none;
          color: palette(medium-grey);
        }
      }
      .c-pag__text {
        @include body-xs--light;
      }
      .page {
        &.disabled {
          @include body-m--medium;
          color: palette(black);
        }
      }
    }
    &__question {
      position: relative;
      border-top: 1px solid palette(light-grey);
      &:last-child {
        border-bottom: 1px solid palette(light-grey);
      }
      &[data-collapse] {
        [data-tab] {
          transition: max-height 0.3s var(--anim-curve);
        }
        .icon-chevron-down {
          transition: 0.3s var(--anim-curve);
          rotate: 180deg;
        }
      }
      &.collapsed {
        .icon-chevron-down {
          transition: 0.3s var(--anim-curve);
          rotate: 0deg;
          &:before {
            transform: scaleY(1);
          }
        }
      }
      &__title {
        display: flex;
        align-items: center;
        position: relative;
        padding-top: 16px;
        padding-right: 10px;
        padding-bottom: 16px;
        cursor: pointer;
        gap: 16px;
        h2 {
          @include body-m--light;
          flex: 1 1 auto;
          margin-bottom: 0;
        }
        >i {
          align-self: center;
          font-size: 18px;
        }
        &.collapsed &__copy-link {
          display: none;
        }
        &__copy-link {
          position: relative;
          display: none;
          font-size: 12px;
          line-height: 1.4;
          color: #7F7F7F;
          outline: none;
          align-self: flex-start;
          cursor: pointer;

          &:focus {
            outline: none !important;
          }
          &:before {
            margin-right: 5px;
            line-height: 1.4;
          }
          &.copied {
            color: palette(black);
            .label-copy {
              display: none;
            }
            .label-copied {
              display: block;
            }
          }
          .label-copied {
            display: none;
          }
        }
      }
      &__answer {
        &__text {
          margin-bottom: 16px;
          a,li,p,span, strong {
            @include body-s--light;
            font-size: 1.4rem !important;
            line-height: 1 !important;
            color: palette(dark-grey) !important;
          }
          li,p,strong {
            font-weight: $font-300 !important;
          }
          a, a>span {
            font-weight: $font-500 !important;
            text-decoration-line: underline !important;
          }
          li {
            margin-left: 0 !important;
            list-style: inherit;
          }
          strong {
            font-weight: $font-600 !important;
          }
        }
        &__category {
          @include body-xs;
          color: palette(dark-grey);
          margin-top: 20px;
          margin-bottom: 16px;
          text-transform: uppercase;
          >span {
            @include body-xs--medium;
            color: palette(black);
            text-transform: none;
          }
        }
      }
    }
  }
  &-suggestions {
    width: 100%;
    position: absolute;
    top: 105%;
    left: 0;
    right: 0;
    background-color: palette(white);
    box-shadow:  0 5px 15px -10px rgb(0, 0, 0);
    z-index: 6;
    &__singlefaq {
      cursor: pointer;
      padding: 16px 24px 16px 32px;
      border-bottom: 1px solid palette(lighter-grey);
      &__title {
        @include body-m--light;
      }
      &__category {
        @include body-xxs;
        text-transform: uppercase;
        text-align: center;
        padding: 2px 6px;
        background-color: palette(white);
        display: none;
      }
      &:hover {
        background-color: palette(ultra-light-grey);
      }
    }
  }
}

@media #{$until-medium} {
  .yext-faq {
    &-list {
      margin-top: 24px;

      &__question {
        &__answer {
          &__category {
            display: block;
          }
        }

        &__title {
          margin-bottom: 48px;

          &.collapsed {
            margin-bottom: 0;
          }
          &__copy-link {
            display: inline-flex;
            align-items: center;
            position: absolute;
            bottom: -24px;
            gap: 5px;

            .collapsed + & {
              display: none;
            }
            >i {
              display: block;
            }
          }
        }
      }
      &.margin-top-l {
        margin-top: 32px;
      }
    }
    &-suggestions {
      width: auto;
      margin: 0 16px;
    }
  }
}

@media #{$from-medium} {
  .yext-faq {
    &-list {
      &__question {
        &__answer {
          padding-right: 24px;
        }
        &__title {
          &__copy-link {
            display: flex;
          }
          &.collapsed &__copy-link {
            display: none;
          }
          >i {
            display: block;
          }

          &__category {
            display: none;
          }

          &__copy-link {
            color: palette(black);
            width: 20px;
            align-self: center;
            > span {
              display: none;
              position: absolute;
              z-index: 3;
              bottom: 30px;
              left: 50%;
              padding: 8px 12px;
              font-size: 12px;
              white-space: nowrap;
              background-color: palette(white);
              box-shadow:  0 5px 10px 0 rgba(0,0,0,0.2);
              transform: translateX(-50%);
              &:before {
                display: block;
                content: '';
                position: absolute;
                left: 50%;
                top: 100%;
                z-index: 3;
                width: 0;
                height: 0;
                border-style: solid;
                border-width: 8px 5px 0 5px;
                border-color: palette(white) transparent transparent transparent;
                transform: rotate(0deg) translateX(-50%);
              }
            }

            &:before {
              margin: 0;
            }
            &:hover {
              > span {
                &.label-copy {
                  display: block;
                }
              }
            }
            &.copied {
              &:hover {
                > span {
                  &.label-copy {
                    display: none;
                  }
                  &.label-copied {
                    display: block;
                  }
                }
              }
            }
          }
        }
      }
      &.margin-top-l {
        margin-top: 48px;
      }
    }
    &-suggestions {
      &__singlefaq {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 1px;
        &__category {
          display: block;
        }
      }
    }
  }
}
