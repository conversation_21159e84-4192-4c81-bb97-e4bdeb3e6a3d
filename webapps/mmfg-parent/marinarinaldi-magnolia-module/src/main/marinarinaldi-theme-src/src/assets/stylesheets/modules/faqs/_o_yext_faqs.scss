@import "../../basics/b_collapsable";

.yext-faq {
  &-breadcrumbs,
  &-header__container,
  &-cards-categories,
  &-contactus,
  &-list,
  &-list-pager,
  &__actualpage,
  &__no-result,
  &__title {
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
  }
  &__actualpage {
    @include body-xs--light;
    color: palette(dark-grey);
    margin-top: 24px;
    margin-bottom: 0;
    text-align: center;
  }
  &__container {
    @include collapsable;
    margin-bottom: 48px;
  }
  &__no-result {
    @include body-s;
    text-align: center;
    color: palette(black);
    max-width: 340px;
    margin-bottom: 16px;
    margin-top: 40px;
    &__title {
      margin-bottom: 16px;
    }
  }
  &__title {
    @include h6;
    text-align: center;
  }
  &-header,
  &__container {
    display: grid;
    grid-template-rows: auto;
    grid-column-gap: 16px;
    @media #{$until-medium} {
      grid-template-columns: repeat(4, 2fr);
    }
    @media #{$from-medium} {
      grid-template-columns: repeat(12, 2fr);
    }
  }
  &-list-pager {
    margin-top: 24px;
    margin-bottom: 0px;
  }

  &-cards-categories {
    margin-top: 60px;
    margin-bottom: 40px;
  }
}

@media #{$until-medium} {
  .yext-faq {
    &__container {
      padding-right: 8px;
      padding-left: 8px;
    }
    &__title {
      margin-top: 32px;
    }
    &__no-result {
      padding: 0 20px;
    }
    &-breadcrumbs,
    &-header__container,
    &-cards-categories,
    &-contactus,
    &-list,
    &-list-pager,
    &__actualpage,
    &__no-result,
    &__title {
      grid-column-start: 1;
      grid-column-end: 5;
    }
  }
}

@media #{$from-medium} {
  .yext-faq {
    &__title {
      margin-top: 40px;
      margin-bottom: 24px;
    }
    &-breadcrumbs {
      grid-column-start: 2;
      grid-column-end: 10;
      max-width: unset;
    }
    &__actualpage {
      margin-top: 40px;
      margin-bottom: 24px;
    }
    &-header__container,
    &-cards-categories,
    &-contactus,
    &-list,
    &-list-pager,
    &__actualpage,
    &__no-result,
    &__title {
      grid-column-start: 4;
      grid-column-end: 10;
    }
  }
}

@media #{$only-large} {
  .yext-faq {
    &-breadcrumbs {
      grid-column-start: 2;
      grid-column-end: 12;
    }
    &-cards-categories,
    &-contactus,
    &-list,
    &-list-pager,
    &__actualpage,
    &__no-result,
    &__title {
      grid-column-start: 2;
      grid-column-end: 12;
    }
  }
}
