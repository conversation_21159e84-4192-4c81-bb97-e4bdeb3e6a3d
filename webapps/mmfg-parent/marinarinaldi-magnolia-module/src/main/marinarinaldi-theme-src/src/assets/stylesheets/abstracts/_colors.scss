/* ===PALETTE SETUP=== */
/* Replace the following colors with the one you actually need for your project. */

//<PERSON> 4.0 COLOR PALETTE
$palette: (
  black: #000000,
  white: #FFFFFF,
  ultra-light-grey: #F9F9F9,
  lighter-grey: #EAEAEA,
  light-grey: #D8D8D8,
  medium-grey: #9A9A9A,
  dark-grey: #595959,
  darker-grey: #262425,
  red-error: #D93025,
  orange: #FABB05,
  yellow: #FFF200,
  green-confirmation: #55D800,
  blue: #2F6EB4,
  blue-a11y: #005FD1,
  error: #FF2000,
  sale: #720700,
  // ORDER STATUS
  processing: #EAEAEA,
  shipped: #9A9A9A,
  partially_completed: #CEE1D6,
  completed: #5C9374,
  cancelled: #F20000,
  green-fiche: #17871D
);

/* ===GETTING COLORS FROM PALETTE=== */
/*
 * The function palette is used to retrieve a color from the palette
 *
 */
@function palette($color: 'black') {
  @if map-has-key($palette, $color) {
    @return map-get($palette, $color);
  }
  @warn "Unknown `#{$color}` in palette";
  @return '';
}

@mixin background-color() {
  @each $color, $_ in $palette {
    .--bg-#{"" + $color} {
      background-color: #{palette($color)};
    }
  }
}

@mixin text-color() {
  @each $color, $_ in $palette {
    .--txt-#{"" + $color} {
      color: #{palette($color)};
    }
  }
}

