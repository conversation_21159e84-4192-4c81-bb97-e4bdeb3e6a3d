html {
  box-sizing: border-box;
  //scroll-behavior: smooth; /* Supported by firefox and Chrome only. Use a fallback for unsupported browsers, if you need. */
  /**
   * We are assuming to be dealing with a browser set to ‘medium’ text.
   * The default size for ‘medium’ text in all modern browsers is 16px
   * We'll be using rem unit throughout the project, this meaning that
   * if you need to reduce the base font size for the entire document
   * you can simply edit this percentage value.
   * Ref: https://snook.ca/archives/html_and_css/font-size-with-rem
   */
  font-size: 14px;
  scrollbar-color: palette(black) palette(white);
  scrollbar-width: thin;
  -webkit-scrollbar-button: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  *,
  *:before,
  *:after {
    /**
     * Use inheritance here in order to make easier
     * to change the box-sizing in plugins or other components that leverage other behavior.
     * Credits: <PERSON> http://blog.teamtreehouse.com/box-sizing-secret-simple-css-layouts#comment-50223
     */
    box-sizing: inherit;
  }
  .loading,
  .loading-cta {
    cursor: wait !important;
  }
}

body {
  box-sizing: border-box;
  line-height: 1.3;
  color: palette(black);
  font-weight: $font-400;
  font-family: $base-font;
  letter-spacing: 0.5px;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  position: relative;
  --anim-curve: cubic-bezier(0.19, 0.43, 0.37, 1);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: $font-400;
}

a {
  color: currentColor;
  cursor: pointer;
  text-decoration: none;
  &:hover {
    color: currentColor;
  }
}

fieldset {
  margin: 0;
  padding: 0;
  min-width: 0;
  border: 0;
}

img {
  display: inline-block;
  max-width: 100%;
  height: auto;
}

figure {
  margin: 0;
}

picture {
  display: inline-block;
}

ul,
li {
  margin: 0;
  padding: 0;
  list-style: none;
}

// a11y - focus state
:focus {
  outline: none;
  [data-whatinput="keyboard"] & {
    @include focus;
  }
}

// HIDE PRINT TEMPLATE on screen media query
// do not edit this class
// -----------------------------------------

#print-template {
  display: none;
}

.debug {
  border: 2px dashed red !important;
  & .debug {
    border: 2px dashed orange !important;
    & .debug {
      border: 2px dashed cyan !important;
    }
  }
}
