import Component from '../abstracts/Component';

export default class CollapseComponent extends Component {

  get COMPONENTNAME() {
    return 'CollapseComponent';
  }

  get SELECTORS() {
    return {
      trigger: '[data-trigger]',
      tab: '[data-tab]',
    };
  }

  get CLASSES() {
    return {
      collapsed: 'collapsed'
    }
  }

  constructor(compEl) {
    super(compEl);
  }

  readDOM() {
    return {
      triggers: this.$component.querySelectorAll(this.SELECTORS.trigger),
      tabs: this.$component.querySelectorAll(this.SELECTORS.tab),
      currentOpenTab: null,
    };
  }

  //get all the tabs and set the height
  init() {
    if(this.dom.tabs) {
      this.dom.tabs.forEach(tab => {
        tab.style.height = tab.offsetHeight + "px";
      });
    }
    if(this.dom.triggers) {
      this.dom.triggers.forEach(trigger => {
        if(trigger.dataset.trigger === "start-open") {
          this.dom.currentOpenTab = trigger;
        } else {
          trigger.classList.add(this.CLASSES.collapsed);
        }
      });
    }
  }

  //handle click on trigger element
  handleCollapse() {
    if(this.dom.triggers) {
      this.dom.triggers.forEach(trigger => {
        trigger.addEventListener("click", () => {
          if(this.dom.currentOpenTab === trigger) {
            // click on the same trigger to close the section
            trigger.classList.add(this.CLASSES.collapsed);
            this.dom.currentOpenTab = null;
          } else if(this.dom.currentOpenTab != null) {
            //another tab is currently open
            this.dom.currentOpenTab.classList.add(this.CLASSES.collapsed);
            trigger.classList.remove(this.CLASSES.collapsed);
            this.dom.currentOpenTab = trigger;
          } else {
            //is the first trigger clicked
            trigger.classList.remove(this.CLASSES.collapsed);
            this.dom.currentOpenTab = trigger;
          }
        });
      });
    }
  }

  bindEvents() {
    this.handleCollapse();
    window.addEventListener('resize', () => {
      if (this.dom.currentOpenTab) {
        const tab = this.dom.currentOpenTab.parentNode.querySelector(this.SELECTORS.tab);
        tab.style.maxHeight = tab.scrollHeight + "px";
      }
    });
  }

  render() {
    super.render();
    this.log('Rendering...');
    this.dom = this.readDOM();
    this.init();
    this.bindEvents();
  }
}
