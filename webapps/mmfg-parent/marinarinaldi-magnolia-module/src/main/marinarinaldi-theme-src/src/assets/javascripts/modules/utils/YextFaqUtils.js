/**
 * This utility class helps to manage the Yext Faqs
 */
import {emit} from './EventsBus';
import {CustomEvents} from './CustomEvents';

class YextFaqUtils {

  constructor() {
    this.readParams();
  }

  get SELECTORS() {
    return {
      input: '[data-yext-input]',
      baseUrl: 'data-base-url',
      searchURI: 'data-search-uri',
      apiKey: 'data-api-key',
      experienceKey: 'data-experience-key',
      verticalKey: 'data-vertical-key',
      siteCountry: 'data-site-country'
    }
  }

  readParams() {
    let params = {};
    params.apikey = document.querySelector('['+this.SELECTORS.apiKey+']').getAttribute(this.SELECTORS.apiKey);
    params.experienceKey = document.querySelector('['+this.SELECTORS.experienceKey+']').getAttribute(this.SELECTORS.experienceKey);
    params.verticalKey = document.querySelector('['+this.SELECTORS.verticalKey+']').getAttribute(this.SELECTORS.verticalKey);
    params.baseUrl = document.querySelector('['+this.SELECTORS.baseUrl+']').getAttribute(this.SELECTORS.baseUrl);
    params.searchURI = document.querySelector('['+this.SELECTORS.searchURI+']').getAttribute(this.SELECTORS.searchURI);
    params.siteCountry = document.querySelector('['+this.SELECTORS.siteCountry+']').getAttribute(this.SELECTORS.siteCountry);
    // for public
    this.params = params;
  }

  getDate() {
    let today = new Date();
    return today.getFullYear()+String(today.getMonth() + 1).padStart(2, '0')+String(today.getDate()).padStart(2, '0');
  }

  buildQS() {
    let qs = '?';
    qs += 'api_key=' + this.params.apikey;
    qs += '&v=' + this.getDate();
    qs += '&locale=' + document.body.getAttribute("data-language");
    qs += '&experienceKey=' + this.params.experienceKey;
    qs += '&verticalKey=' + this.params.verticalKey;
    qs += '&limit=10';
    qs += '&filters={"c_site":{"$eq":"'+this.params.siteCountry+'"}}';
    return qs;
  }

  buildUrl() {
    // base url + uri + query params
    let url = this.params.baseUrl + '/' + this.params.searchURI + this.buildQS();
    return url;
  }

  getYextFaq(input) {
    // context
    let self = this;
    // build final url
    let finalUrl = encodeURI(this.buildUrl() + '&input=' + input);
    // go fetch faq
    fetch(finalUrl)
      .then(res => res.json())
      .then(out => self.postProcessing(out.response, input))
      .catch(err => console.log(err));
  }

  postProcessing(results, input) {
    let payload = {};
    payload.faqs = results;
    payload.input = input;
    emit(CustomEvents.YEXT_EVENTS['faqsLoaded'], payload, this.SELECTORS.input);
  }
}

export default new YextFaqUtils();
