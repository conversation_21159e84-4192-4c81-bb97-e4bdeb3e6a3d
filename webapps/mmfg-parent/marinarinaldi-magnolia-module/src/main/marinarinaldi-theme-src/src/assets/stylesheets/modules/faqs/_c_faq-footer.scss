.yext-faq {
  &-cards {
    &-categories {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      &__singlecard {
        border: 1px solid palette(medium-grey);
        padding: 24px;
        a {
          display: flex;
          flex-direction: column;
          flex-wrap: wrap;
          align-items: center;
          text-decoration: none;
          &:hover {
            text-decoration: inherit;
          }
          img {
            max-width: 48px;
            max-height: 48px;
          }
        }
        &__title {
          @include body-s--medium;
          text-transform: uppercase;
        }
        &__text {
          @include body-xs;
        }
        &:hover,
        &.active {
          border-color: palette(black);
        }
        img,
        &__title {
          margin-bottom: 4px;
        }
      }
    }
  }
  &-contactus {
    text-align: center;
    &__cta {
      display: flex;
      justify-content: center;
      a {
        @include body-s;
        text-decoration: none;
        text-transform: uppercase;
        width: 100%;
        max-width: 235px;
        padding: 12px 24px;
        border: 1px solid palette(black);
        >span {
          margin: 0 auto;
        }
      }
    }
    &__title {
      @include body-s--light;
      margin-bottom: 16px;
    }
  }
}

@media #{$until-medium} {
  .yext-faq {
    &-contactus {
      &__cta {
        a {
          max-width: 200px;
        }
      }
    }
  }
}

@media #{$from-medium} {
  .yext-faq {
    &-cards {
      &-categories {
        grid-template-columns: repeat(4, 1fr);
        &.cards_3 {
          grid-template-columns: repeat(4, 1fr);
        }
        &.cards_4 {
          grid-template-columns: repeat(4, 1fr);
        }
        &.cards_5 {
          grid-template-columns: repeat(6, 1fr);
          > div {
            grid-column: span 2;
            &:nth-child(1) {
              grid-column: span 3;
            }
            &:nth-child(2) {
              grid-column: span 3;
            }
          }
        }
        &.cards_6 {
          grid-template-columns: repeat(3, 1fr);
        }
        &.cards_7 {
          grid-template-columns: repeat(12, 1fr);
          > div {
            grid-column: span 3;
            &:nth-child(1) {
              grid-column: span 4;
            }
            &:nth-child(2) {
              grid-column: span 4;
            }
            &:nth-child(3) {
              grid-column: span 4;
            }
          }
        }
        &.cards_8 {
          grid-template-columns: repeat(4, 1fr);
        }
      }
    }
  }
}
