export const CustomEvents = {
  AJAX_EVENTS: {
    loaded: 'ajax:loaded',
    updated: 'ajax:updated',
  },
  LOADER_EVENTS: {
    show: 'loader:show',
    hide: 'loader:hide',
    showLocal: 'loader:showlocal',
    hideLocal: 'loader:hidelocal',
    visible: 'loader:visible',
    hidden: 'loader:hidden',
  },
  LOAD_COMPONENT: {
    simplebar: 'load:simplebar',
  },
  KEYBOARD_EVENTS: {
    escape: 'ku:key:escape',
    enter: 'ku:key:enter',
    tab: 'ku:key:tab',
  },
  MQ_EVENTS: {
    xs: {
      in: 'mq.xs.in',
      out: 'mq.xs.out',
      current: 'mq.xs.cur',
    },
    sm: {
      in: 'mq.sm.in',
      out: 'mq.sm.out',
      current: 'mq.sm.cur',
    },
    md: {
      in: 'mq.md.in',
      out: 'mq.md.out',
      current: 'mq.md.cur',
    },
    lg: {
      in: 'mq.lg.in',
      out: 'mq.lg.out',
      current: 'mq.lg.cur',
    },
    xl: {
      in: 'mq.xl.in',
      out: 'mq.xl.out',
      current: 'mq.xl.cur',
    },
  },
  SEARCH_LAYER_EVENTS: {
    open: 'search.layer:open',
    close: 'search.layer:closed',
  },
  BACKDROP_EVENTS: {
    show: 'backdrop:show',
    hide: 'backdrop:hide',
    hidden: 'backdrop:hidden',
    shown: 'backdrop:shown',
  },
  LISTING_CAROUSEL_EVENTS: {
    show: 'plp:carousel:show',
  },
  PRODUCT_EVENTS: {
    addtocart: 'product:addtocart',
    addedtocart: 'product:addedtocart',
    sizerequired: 'product:sizerequired',
    sizechanged: 'product:sizechanged',
    notifyme: 'product:notifyme',
    notifymeclose: 'product:notifymeclose',
    notifymesuccess: 'product:notifymesuccess',
    notifymereset: 'product:notifymereset',
    addtowish: 'product:addtowish',
    addedtowish: 'product:addetowish',
    removefromwish: 'product:removefromwish',
    removedfromwish: 'product:removedfromwish',
  },
  SIZES_EVENTS: {
    sizesopen: 'sizes:open',
    sizesclose: 'sizes:close',
    sizestoggle: 'sizes:toggle',
    sizesupdate: 'sizes:update',
    noSizeSelected: 'sizes:nosize',
  },
  MINICART_EVENTS: {
    open: 'minicart:open',
    updateqty: 'minicart:updateqty',
    update: 'minicart:update',
  },
  MODAL_EVENTS: {
    show: 'modal:show',
    hide: 'modal:hide',
    shown: 'modal:shown',
    hidden: 'modal:hidden',
    afterOpen: 'modal:afterOpen',
    afterClose: 'modal:afterClose',
    afterUpdateContent: 'modal:afterUpdate',
  },
  SORT_BY_EVENTS: {
    changed: 'sortBy:changed',
  },
  FILTERS_EVENTS: {
    updated: 'query:updated',
    pageLoaded: 'ajaxPage:loaded',
    active: 'filters:active',
  },
  PAGE_LAYOUT_EVENTS: {
    changed: 'pageLayout',
  },
  INFINITE_SCROLL_EVENTS: {
    next: 'infiniteScroll:next',
    loaded: 'infiniteScroll:loaded',
    animation: 'infiniteScroll:animation',
  },
  COOKIE: {
    accepted: 'cookie:accepted',
  },
  RETURN_EVENTS: {
    subsituteProduct: 'product:substituted',
  },
  CAROUSEL: {
    carouselLoaded: 'carousel:loaded',
    triggerCarousel: 'carousel:trigger',
  },
  MENU_EVENTS: {
    showMenu: 'menu:show',
    hideMenu: 'menu:hide',
    active: 'menu:active',
    inactive: 'menu:inactive',
  },
  CHECKOUT_EVENTS: {
    showInvoiceBox: 'invoice:show',
    showInvoiceFormLabels: 'invoiceLabels:show',
    hideInvoiceBox: 'invoice:hide',
    showShippingForm: 'shipping:show',
    hideShippingForm: 'shipping:hide',
    paymentSelected: 'payment:selected',
    privacyChange: 'privacy:change',
  },
  SHIPMENT_EVENTS: {
    valid: 'checkout:proceed',
    invalid: 'checkout:stop',
  },
  PAYMENTS_EVENTS: {
    paypal: 'paypal:token',
  },
  CHANGE_AVAILABLE_LAYER: {
    available: 'availableChange:true',
  },
  CHANGE_RETURN_STEPS: {
    enableConfirm: 'confirmBtn:enabled',
    disabledConfirm: 'confirmBtn:disabled',
  },
  SIZE_GUIDE: {
    openLayer: 'sizeguide:open',
    closeLayer: 'sizeguide:close',
  },
  NOTIFY_ME_STATE: {
    sent: 'notifyme:sent',
  },
  TRACKING: {
    loadMore: 'track:loadMore',
  },
  O2O: {
    ccManualSearch: 'StoreLocator:Search:Place',
    ccStoreSelect: 'StoreLocator:Store:Select',
    ccStoresShowResults: 'StoreLocator:Stores:ShowResults',
  },
  VALIDATION: {
    bindValidation: 'validation:bind',
  },
  PDP_LAYER_EVENTS: {
    open: 'pdpLayer:open',
    close: 'pdpLayer:close',
  },
  NL_MODULE_EVENTS: {
    error: 'nlModule:error',
    show: 'nlModule:show',
    hide: 'nlModule:hide',
    shown: 'nlModule:shown',
    hidden: 'nlModule:hidden',
    afterOpen: 'nlModule:afterOpen',
    afterClose: 'nlModule:afterClose',
    subscribed: 'nlModule:subscribed',
  },
  NL_POPUP_EVENTS: {
    close: 'nlPop:hide',
  },
  NL_FOOTER_EVENTS: {
    error: 'nlPop:error',
    show: 'nlPop:show',
    hide: 'nlPop:hide',
    shown: 'nlPop:shown',
    hidden: 'nlPop:hidden',
    afterOpen: 'nlPop:afterOpen',
    afterClose: 'nlPop:afterClose',
    subscribed: 'nlPop:subscribed',
  },
  POPUP_EVENTS: {
    pauseTimer: 'popup:pause',
    resumeTimer: 'popup:resume',
    stopTimer: 'popup:stop',
  },
  COLLAPSE_EVENTS: {
    update: 'collapse:update',
    toggle: 'collapse:toggle',
  },
  ACCOUNT_EVENTS: {
    pwdResetForm: 'resetForm:show',
    addressLabels: 'labels:show',
  },
  TOOLTIP_EVENTS: {
    create: 'createTooltip:create',
    destroy: 'destroyTooltip:destroy',
  },
  STORELOCATOR_EVENTS: {
    clearFilters: 'filters:clear',
    hideReset: 'filters:hideReset',
    showReset: 'filters:showReset',
    updateCount: 'filters:updateCount',
  },
  SIMPLEBAR_EVENTS: {
    loaded: 'simpleBar:loaded'
  },
  YEXT_EVENTS: {
    faqsLoaded: 'faqs:loaded'
  }
};
