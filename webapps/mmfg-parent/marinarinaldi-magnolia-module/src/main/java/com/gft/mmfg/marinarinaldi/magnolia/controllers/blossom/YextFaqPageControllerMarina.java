package com.gft.mmfg.marinarinaldi.magnolia.controllers.blossom;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.gft.mmfg.core.magnolia.controllers.blossom.YextFaqPageController;

import info.magnolia.module.blossom.annotation.ComponentInheritanceMode;
import info.magnolia.module.blossom.annotation.Inherits;
import info.magnolia.module.blossom.annotation.Template;


@Controller
@RequestMapping("/t-yext-faq-page")
@Template(id = "marinarinaldi-magnolia-module:pages/t-yext-faq-page", title = "Yext FAQ page", dialog = "marinarinaldi-magnolia-module:pages/t-yext-faq-page")
@Inherits(components = ComponentInheritanceMode.ALL)
public class YextFaqPageControllerMarina extends YextFaqPageController
{}
