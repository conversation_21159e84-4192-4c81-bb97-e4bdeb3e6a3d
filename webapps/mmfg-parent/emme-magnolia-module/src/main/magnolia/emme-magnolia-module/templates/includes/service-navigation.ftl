<div class="service-navigation">
  <div class="service-navigation__wrapper" data-js-component="CollapseComponent" data-collapse-maxres="md">
    <div class="service-navigation__dropdown" data-trigger="">
        <span class="--uppercase">${content.title}</span>
        <button class="icon-chevron-up"></button>
    </div>
    <div class="service-navigation__list" data-tab="">
      <div id="collapse-nav" class="service-navigation__list__content">
        <nav class="service-navigation__list">
          <ul class="nav-list" >
          [#assign parent = cmsfn.parent(content)!]
          [#list cmsfn.children(parent, 'mgnl:page') as serviceNode]
            [#if mmfgfn.visibleToCurrentSite(serviceNode)]
              <li class="nav-list__item ${((actpage.title!actpage['@name']!'')?contains(serviceNode.title!serviceNode['@name']!''))?then('active' , '')}" data-node-name="${serviceNode.title!serviceNode['@name']!''}" >
                <a class="${((actpage.title!actpage['@name']!'')?contains(serviceNode.title!serviceNode['@name']!''))?then('active' , '')} ${((serviceNode.title!serviceNode['@name']!'') == 'live-chat')?then ('not-available' , '') }" href="${mmfgfn.link(serviceNode.@path)}">${(serviceNode.title!serviceNode['@name']!'')?upper_case}</a>
              </li>
            [/#if]
          [/#list]
          </ul>
        </nav>
      </div>
      <div class="service-navigation__list__faq">
        <div class="service-navigation__list__faq-wrapper">
          ${mmfgfn.msg('text.account.faqInfo.title')}
          [@a_link
          href=faqLink
          cssClass="dashboard-faq underline"
          ]
            ${mmfgfn.msg('text.account.faq')}
          [/@a_link]
        </div>
      </div>
    </div>
  </div>
</div>
