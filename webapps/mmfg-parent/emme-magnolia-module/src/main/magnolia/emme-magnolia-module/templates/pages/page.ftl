[#macro page content='' htmlclass='' bodyClass='' pageClass='' mainClass='' controller=''
             synchLoadControllerCss=false bareHref='' refresh=false shareLink=''
             productCode='' showPrivacyModal=false isXhr=false patternBackgroundImage=''
             patternBackgroundImageMobile=''
             showFooter=false bare=false isSplashPage=false
             allowBack=false discountControlEnabled=false enableZoom=false]

    [#if isXhr!false]
        [#include "../includes/vars.ftl" ]
        [#include "/core-magnolia-module/templates/includes/fieldlength.ftl" /]
        [#-- for xhr request send only contents --]
        <main id="wrapper" role="main" class="${mainclass!}">
            [@cms.area name="main"/]
        </main>
    [#else]
        <!DOCTYPE html>
        [#include "../includes/vars.ftl" ]
        [#include "/core-magnolia-module/templates/includes/fieldlength.ftl" /]

        <html xml:lang="${mmfgfn.getCurrentSessionLanguage()}" lang="${mmfgfn.getCurrentSessionLanguage()}"
              class="no-js ${htmlclass!}"
              data-public-path="${docroot}"
              [#if controller?trim?length>0]
                  data-controller="${controller}"
              [#else]
                  data-controller="${def.parameters.jsController!''}"
              [/#if]
              data-version="${mmfgfn.getDocrootVersion()}"
              data-js-component="TrackingComponent"
              data-verifalia-validation="${mmfgfn.isVerifaliaEmailValidation()?string}"
              [#if cmsfn.isAuthorInstance() && !mmfgfn.develop() ]
                  [#assign multisitePath = "/" + sitefn.site().getName()! /]
              [/#if]
              data-context="${ctx.contextPath}${multisitePath!}"
        >
            <head>
                [@cms.page /]
                [#assign enableZoom=enableZoom]
                [#include "../pages/areas/head.ftl"]
            </head>
            [#if patternBackgroundImage?has_content || patternBackgroundImageMobile?has_content]
                <style scoped>
                    body {
                        background-image: url(${patternBackgroundImage!});
                    }
                    @media (max-width: 768px) {
                        body {
                            background-image: url(${patternBackgroundImageMobile!});
                        }
                    }
                </style>
            [/#if]
            [#include "../includes/page-modals.ftl"]
            [#-- popup utils, contiene regole di visualizzazione popups e timer di comparsa popup --]
            [#include "../includes/popup-utils.ftl"]

            <body class="${bodyClass!}${cmsfn.isEditMode() ?then( ' editmode' , '')} ${cmsfn.isPreviewMode() ?then( ' previewmode' , '')} ${template} ${small?? ?then( ' small', '')}"
                  data-controller="${mmfgfn.templateToController(template)}"
                  data-nl-key="${(selectedModal?? && selectedModal?has_content)?then(selectedModal.key, '')}"
                  data-nl-link="${(selectedModal?? && selectedModal?has_content)?then(mmfgfn.link(selectedModal['@uuid']) , '')}"
                  data-nl-start-time="${(selectedModal?? && selectedModal?has_content)?then(selectedModal.startDate.getTimeInMillis() , '')}"
                  data-nl-end-time="${(selectedModal?? && selectedModal?has_content)?then(selectedModal.endDate.getTimeInMillis() , '')}"
                  data-category="${category!}"
                  data-subcategory="${subcategory!}"
                  data-modal-key="${(onReadyModal?? && onReadyModal?has_content)?then(onReadyModal.key , '')}"
                  data-modal-link="${(onReadyModal?? && onReadyModal?has_content)?then(mmfgfn.link(onReadyModal['@uuid']) , '')}"
                  data-modal-start-time="${(onReadyModal?? && onReadyModal?has_content)?then(onReadyModal.startDate.getTimeInMillis() , '')}"
                  data-modal-end-time="${(onReadyModal?? && onReadyModal?has_content)?then(onReadyModal.endDate.getTimeInMillis() , '')}"
                  data-modal-template="${(onReadyModal?? && onReadyModal?has_content)?then(onReadyModal['mgnl:template']?split( '/')[1] , '')}"
                  data-language="${mmfgfn.getCurrentSessionLanguage()}"
                  data-website="${mmfgfn.getCurrentCountryIsoCode()}"
                  data-product-code="${productCode!}"
                  data-share-link="${shareLink!}"
                  data-is-arvato="${isArvato!}"
                  data-show-privacy-modal="${(showPrivacyModal!false)?string!}"
                  data-iseu="${isEu?string}"
                  data-back-allowed="${allowBack?c}"
                  data-js-layer-is-open="false"
                  data-hide-personal-stylist="${((content.hidePersonalStylist)!false)?string}"
            >
                [#if !(disableanalytics?has_content)][#assign disableanalytics = false][/#if]
                [#if !disableanalytics ][#include "/core-magnolia-module/templates/includes/page-analytics-body.ftl"][/#if]
                [#if user?? && user?has_content]
                    <form disabled="disabled" id="currentUserInfo" hidden="hidden" action="">
                        <input name="name" value="${user.firstName}"/>
                        <input name="surname" value="${user.lastName}"/>
                        <input name="email" value="${user.uid}"/>
                    </form>
                [/#if]

                [#include "../includes/header/header.ftl"]

                <div class="page ${pageClass!} ${bare?then('--fixed-height', '')}" id="page-wrapper">
                    <main class="${mainClass!} main" role="main" id="wrapper">
                        [#nested]
                    </main>
                    [#include "../includes/footer/footer.ftl"]
                </div>

                [#include "../includes/javascripts.ftl"]
                [#if !isSplashPage ]
                    [#-- communication popup, si vede se non nascosto da configurazioni di pagina --]
                    [#if showCommunicationsPopup!false]
                        [#include "../includes/modal-communication.ftl"]
                    [/#if]
                    [#-- newsletter popup, si vede se non nascosto da configurazioni di pagina e se utente non è già iscritto --]
                    [#if showNewsletterPopup!false && template != 't-homepage' && template != 't-register' && template != 't-login'
                        && template != "t-cart" && template != "t-checkout" && template != 't-unsubscribe-new-privacy'  && template != 't-return-request-order'
                        && template != 't-return-request-modal' && template != "t-myaccount-returns" && !isSplashPage && template != 't-data-enrichment' && template != 't-happy-birthday']
                        [#include "../includes/newsletter/modal-newsletter.ftl"]
                    [/#if]
                    [#-- login popup, si vede se non nascosto da configurazioni di pagina e se utente guest si vede popup login --]
                    [#if showLoginPopup!false]
                      [#include "../includes/login-popup.ftl" ]
                    [/#if]
                [/#if]
                [#include "../includes/js-components.ftl"]
                [#if mmfgfn.isEditMode()]
                    [#include "../includes/projects/debug-seo.ftl"]
                [/#if]
            </body>
        </html>
    [/#if]
[/#macro]
