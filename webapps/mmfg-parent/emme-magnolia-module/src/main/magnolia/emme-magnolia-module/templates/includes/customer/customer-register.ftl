[#macro customerRegister isReturnConfirm=false isCheckoutConfirm=false ]

  [#include "../ui/a.ftl" /]
  [#assign isConfirm = isReturnConfirm || isCheckoutConfirm /]

  <div class="p-register__wrapper">
    <div class="p-register__col form_col">
      <div class="p-register__form">
        [#if !isReturnConfirm]
          <div class="p-register__form__title">${mmfgfn.msg('register.new.customer')}</div>
          <div class="p-register__form__subtitle">${mmfgfn.msg('register.new.customer.subtitle')}</div>
        [/#if]
        [#if isCheckoutConfirm]
          [@a_link
            href="${mmfgfn.link('/register')}"
            variant="--button --primary"
            cssClass="--full-width"
          ]
            <i class="icon-email --s"></i>
            ${mmfgfn.msg('login.not.registered')}
          [/@a_link]
        [#else]
        <div class="p-register__form__registration" data-js-component="RegistrationComponent">
          [#assign urlAction="/register" pwdSuggestion=true /]
          [#include "../form/form-register.ftl" ]
        </div>
        [/#if]
      </div>
    </div>
    <div class="p-register__reg-side">
      <div class="p-register__reg-side__title">${isConfirm?then(mmfgfn.msg('checkout.orderConfirmation.promo.list.title'), mmfgfn.msg('register.benefit.title'))}</div>
      <div class="p-register__reg-side__list">
        [#if isReturnConfirm]
          <ul class="custom-list --return-confirm">
            <li>${mmfgfn.msg('checkout.orderConfirmation.promo.list.one')}</li>
            <li>${mmfgfn.msg('checkout.orderConfirmation.promo.list.two')}</li>
            <li>${mmfgfn.msg('checkout.orderConfirmation.promo.list.three')}</li>
          </ul>
        [#else]
          [#include "customer-register-list.ftl"]
        [/#if]
      </div>
    </div>
  </div>

[/#macro]