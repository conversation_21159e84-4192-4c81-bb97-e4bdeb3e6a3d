[#include "/core-magnolia-module/templates/includes/fieldlength.ftl" /]
[#include "../includes/ui/button.ftl"]
[#include "../includes/ui/input.ftl"]
[#include "../includes/checkout/checkout-form-address.ftl"]

<div class="modal" id="modal-address-content">
  <div class="modal__header">
    <h2 class="modal__title">${mmfgfn.msg('shipping.update')}</h2>
    <p class="modal__subtitle">${mmfgfn.msg('update.shipment.data.popup.description')}</p>
  </div>
  <form
    action="${ctx.contextPath}/checkout/multi/add-delivery-address?modify-address-recover=true&amp;isShippingAddress=true"
    class="form checkout-section"
    commandName="checkoutForm"
    id="checkoutForm"
    method="post"
    data-recover-shipping-address=""
  >
    <input type="hidden" name="shippingAddress.id" id="shippingAddress.id" value="${checkoutForm.shippingAddress.id}" />
    <input type="hidden" name="billingAddressCode" value="${checkoutForm.billingAddressCode!}" />

    <div class="address-box">
      [@input
        name="shippingAddress.firstName"
        placeholder=mmfgfn.msg('address.firstName')
        value=(firstNameAutoFill?? && firstNameAutoFill?has_content)?then(firstNameAutoFill, '')
        required=true
        attrs={
          "maxlength":"FirstName_lenght"
        }
      /]
      [@input
        name="shippingAddress.lastName"
        placeholder=mmfgfn.msg('address.lastName')
        required=true
        value=(lastNameAutoFill?? && lastNameAutoFill?has_content)?then(lastNameAutoFill, '')
        attrs={
          "maxlength":"LastName_lenght"
        }
      /]
      [@checkoutFormAddress
        pathprefix="shippingAddress."
        payment=true
        hideName=true
        hideLabel=true
      /]
    </div>
    [@button
      cssClass="--full-width"
      msg=mmfgfn.msg('text.updates')
      type="submit"
    /]
  </form>
</div>
