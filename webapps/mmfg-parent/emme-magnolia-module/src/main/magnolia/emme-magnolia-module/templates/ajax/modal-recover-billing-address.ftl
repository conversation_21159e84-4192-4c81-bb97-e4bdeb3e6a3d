[#include "/core-magnolia-module/templates/includes/fieldlength.ftl" /]
[#include "../includes/ui/button.ftl"]
[#include "../includes/form/form-new-billing.ftl"]
[#include "../includes/checkout/checkout-form-address.ftl"]

<div class="modal" id="modal-address-content">
  <div class="modal__header">
    <h2 class="modal__title">${mmfgfn.msg('billing.update')}</h2>
    <p class="modal__subtitle">${mmfgfn.msg('update.shipment.data.popup.description')}</p>
  </div>
  <form action="${ctx.contextPath}/checkout/multi/add-delivery-address?modify-address-recover=true&amp;isShippingAddress=false"
    class="form checkout-section"
    data-validate-form=""
    commandName="checkoutForm"
    id="checkoutForm"
    method="post"
    data-recover-billing-address=""
  >
    <input id="billingAddress.id" name="billingAddress.id" type="hidden" value="${checkoutForm.billingAddress.id}"/>
    <div class="address-box" id="editBillingAddress">
      [@formNewBilling hideCountry=true hideNewBillingCheckbox=true hideTitle=true pathprefix="billingAddress." /]
      [@checkoutFormAddress billing=false billingValidation=true hideFiscalCode=true hideLabel=true pathprefix="billingAddress." /]
    </div>
    [@button
      cssClass="--full-width"
      msg=mmfgfn.msg('text.updates')
      type="submit"
    /]
  </form>
</div>
