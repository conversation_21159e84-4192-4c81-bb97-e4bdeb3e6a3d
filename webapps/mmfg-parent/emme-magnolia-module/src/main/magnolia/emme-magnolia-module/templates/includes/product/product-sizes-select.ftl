[#macro productSizesSelect variants='' triggerName='' cssClass='' ]

  <div class="${cssClass}"
    data-js-component="SizeSelectorComponent"
    data-product-code="${product.code}"
    data-sizes-code="${(triggerName?? && triggerName?has_content)?then(triggerName, '') }"
    role="radiogroup"
    aria-label="${mmfgfn.msg('selected.size')!('Select size')}"
  >
    <!-- Messaggi per screen reader -->
    <span class="sr-only" data-size-selected-template="${mmfgfn.msg('selected.size')!('Select size')}" aria-hidden="true"></span>
    <span class="sr-only" data-size-unavailable-template="${mmfgfn.msg('basket.error.product.notfound')!('Out of stock')}" aria-hidden="true"></span>
    
    [#list variants as variant]
      <div class="wrapper">
        <label
          for="id_${variant?index}"
          class="${(variant.stock.stockLevel < 1)?then('unavailable', '') }"
          data-size=""
          tabindex="0"
          aria-label="${mmfgfn.msg("product.size")!("Size")}: ${variant.variantOptionQualifiers[0].value} ${(variant.stock.stockLevel < 1)?then(mmfgfn.msg("basket.error.product.notfound")!("Out of stock"), '')}"
        >
          <input
            type="checkbox"
            name="size"
            data-size-value="${variant.variantOptionQualifiers[0].value}"
            value="${variant?index}"
            id="id_${variant?index}"
            data-product-code="${variant.code}"
            data-unavailable="${(variant.stock.stockLevel < 1)?then('true', '') }"
            data-low-stock="${(variant.stock.stockLevel <= 5 && variant.stock.stockLevel > 0)?then('true', 'false') }"
            data-is-wishlist="${(sapfn.isProductInWishlist(variant.code, true))?then('full', 'empty')}"
            data-tracking-action="sizeSelection"
            autocomplete="off"
            aria-checked="false"
            ${(variant.stock.stockLevel < 1)?then('disabled aria-disabled="true"', '')}
          />
          <span>${variant.variantOptionQualifiers[0].value}</span>
        </label>
      </div>
    [/#list]
    <p class="pdp__info__sizes__low-stock --hidden" data-js-low-stock="">${mmfgfn.msg("product.sizes.lowstock")}</p>
    <p class="--msg-error --xxs --hidden" style="flex:100%;"data-js-no-size="">${mmfgfn.msg('product.select.a.size')}</p>
  </div>

[/#macro ]
