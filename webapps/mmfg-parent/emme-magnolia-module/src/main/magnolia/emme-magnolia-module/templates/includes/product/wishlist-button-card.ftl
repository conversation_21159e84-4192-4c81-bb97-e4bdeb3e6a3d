[#macro wishlistButtonCard product currentWishlist='']

	[#assign productCode = product.code]

	[#if currentWishlist?? && currentWishlist?has_content ]
		[#assign isInWishlist = sapfn.isProductInWishlist(product, currentWishlist)]
  [#else]
    [#assign isInWishlist = sapfn.isProductInWishlist(product)]
  [/#if]

  <button class="icon-wishlist-${isInWishlist?then('full','empty')} js-wishlist-cta --pointer product-card--wishlist${isInWishlist ?then (' js-added-wish js-remove-from-wish','-grey js-add-to-wish')}"
      data-js-component="WishlistComponent"
      data-js-wishlist="${isInWishlist?then('full','empty')}"
      data-loginurl="${mmfgfn.toHttps(mmfgfn.link('/action/wishlist/login?'))}"
      data-code="${productCode}"
      data-wish-remove-url="${ctx.contextPath}/action/miniwishlist/remove?productCode=${productCode}"
      data-wish-add-url="${ctx.contextPath}/action/ajax/wishlist-add?code=${productCode}"
      data-href="${ctx.contextPath}/action/${sapfn.isProductInWishlist(product)?then ('ajax/wishlist-add','miniwishlist/remove?productCode=')}${isInWishlist ?then (product.code,'')}"
      data-label-add="${mmfgfn.msg('checkout.addwishlist.move')}"
      data-label-remove="${mmfgfn.msg('wishlist.product.remove')}"
      aria-label="${isInWishlist?then(mmfgfn.msg('wishlist.product.remove'),mmfgfn.msg('checkout.addwishlist.move'))}"
      >
  </button>
[/#macro]