.c-main-header {
  &__mini-cart {
    position: relative;
    &__icon {
      display: block;
      position: relative;
    }
    &__counter {
      @include body-xxs($base-font, $font-600);
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: -10px;
      right: -11px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
    }
    @media #{$from-large} {
      &:hover, &:focus-within {
         .l-quick-cart {
          opacity: 1;
          visibility: visible;
          z-index: $z-i-header-layer;
          pointer-events: all;
          transform: translate(0, 0);
        }
      }
    }
    .l-quick-cart__notification.--open {
      .l-quick-cart {
        opacity: 1;
        z-index: $z-i-header-layer;
        pointer-events: all;
        transform: translate(0, 0);
      }
    }
    .l-quick-cart {
      visibility: hidden;
      opacity: 0;
      z-index: -10;
      pointer-events: none;
      transform: translate(0, -15px);
      transition: transform 0.5s var(--anim-curve);
    }
  }
}
