@import "../../basics/b_collapsable";

.service-container {
  .service-navigation {
    &__dropdown .collapsed{
      visibility: hidden;
    }
    &__list {
      .nav-list {
        text-transform: uppercase;
        &__item {
          border-bottom: 1px solid color(mid-grey);
          &:not(.active) {
            &:hover {
              a {
                @include body-xs($base-font, $font-600);
              }
            }
          }
        }
      }
      &__faq {
        @include body-xxs;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 2px;
        padding: 0 16px;
        margin-top: 32px;
        margin-bottom: 6px; // give dropdown logic space to show underline on logout link
        a {
          color: currentColor;
        }
        &-wrapper {
          color: color(dark-grey);
          a {
            @include body-xxs;
            display: inline;
            color: color(dark-grey);
          }
        }
      }
    }
  }
}

@media #{$from-large} {
  .service-container {
    .service-navigation {
      &__list {
        &__faq {
          padding: 0;
          text-align: left;
        }
      }
    }
  }
  .service-navigation {
    &__dropdown {
      display: none;
    }
    &__list {
      .nav-list {
        border-style: solid;
        border-width: 1px 1px 0;
        border-color: color(mid-grey);
        &__item {
          a {
            @include body-xs;
            display: block;
            padding: 18.5px 16px;
          }
          &.active {
            display: block;
            border-right: 4px solid color(light-blue);
            a {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}

@media #{$until-large} {
  .service-navigation {
    @include collapsable();
    &__dropdown {
      border-bottom: 1px solid color(neutral);
      border-top: 1px solid color(neutral);
      position: relative;
      span {
        margin: 16px;
        @include h8;
      }
      .icon-chevron-up {
        display: flex;
        position: absolute;
        right: 16px;
      }
    }
    &__list {
      &.collapsed {
        visibility: hidden;
      }
      .nav-list {
        a {
          margin: 8px 16px;
          @include body-xs;
        }
        &__item {
          display: flex;
          &.active {
            display: none;
          }
        }
      }
    }
  }
}
