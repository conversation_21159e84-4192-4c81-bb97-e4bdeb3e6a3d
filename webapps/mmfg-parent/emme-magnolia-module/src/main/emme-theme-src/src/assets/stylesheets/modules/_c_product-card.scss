.c-product-card {
  .product-card {
    &__wrapper {
      position: relative;
      > a {
        display: flex;
        flex-direction: column;
        row-gap: 16px;
      }
    }
    &__wishlist {
      [class*="icon-wishlist-"] {
        @include icon-size(24);
        display: flex;
        justify-content: center;
      }
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 1;
    }
    &__carousel {
      display: flex;
      overflow: hidden;
      height: auto;
    }
    &__picture {
      min-width: 100%;
      min-height: 100%;
      picture,
      img {
        min-width: 100%;
        min-height: 100%;
      }
    }
    &__arrow {
      opacity: 0;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 1;
      i,
      button {
        @include icon-size(14);
        width: 24px;
        height: 24px;
        display: flex;
        border-radius: 50%;
        align-items: center;
        justify-content: center;
      }
      &.--prev {
        left: 10px;
      }
      &.--next {
        right: 10px;
      }
    }
    &__info__wrapper,
    &__info__text {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      row-gap: 4px;
      .product-tag-label {
        display: flex;
        height: 22px;
        padding: 4px 16px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 200px;
      }
    }

    &__info__text {
      row-gap: 8px;
      text-align: center;

      .product-tag-label {
        margin-top: 8px;
      }
    }

    &__name {
      @include body-xs($alt-font);
    }

    &__swatches {
      display: flex;
      column-gap: 24px;
      row-gap: 8px;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      margin-top: 24px;

      .swatch-text {
        @include body-xs;
        cursor: pointer;
      }
      .swatch-color {
        width: 12px;
        height: 12px;
        a {
          display: block;
          width: 100%;
          height: 100%;
          img {
            display: block;
            width: 100%;
            height: 100%;
            border-radius: 50%;
          }
        }
      }
    }
    &__info-container-price {
      display: flex;
      column-gap: 4px;
      @include body-xs;
      .price-discount {
        @include body-xs($base-font, $font-600);
        color: color(web-sale-red);
      }
      .price-full:not(:last-child) {
        text-decoration: line-through;
      }
    }
  }
}

@media #{$until-medium} {
  .c-product-card {
    .product-card {
      &__wishlist {
        > a[class*="icon-wishlist"] {
          @include icon-size(16);
          width: 16px;
          height: 16px;
        }
      }
      &__info__text {
        a {
          display: grid;
        }
      }
      &__arrow {
        display: none;
      }
      &__carousel[data-js-index="0"].bounce {
        picture {
          &:first-child,
          &:nth-child(2) {
            animation: bounce 1.2s ease-in-out;
          }
        }
      }
    }
  }
}

@media #{$from-medium} {
  .c-product-card {
    .product-card {
      &__wishlist {
        right: 16px;
        top: 16px;
      }
      &__images {
        position: relative;
        overflow: hidden;
      }
      &__wrapper {
        > a {
          &:focus-within,
          &:focus {
            .product-card__arrow {
              opacity: 1;
            }
          }
        }
      }
      &__images__wrapper:hover,
      &__images__wrapper:focus-within {
        .product-card__arrow {
          opacity: 1;
        }
      }
      &__info__text,
      &__info__wrapper {
        row-gap: 12px;
      }

      &__info__text {
        .product-tag-label {
          margin-top: 4px;
        }
      }
    }
  }
}

@media #{$from-large} {
  .c-product-card {
    .product-card {
      &__wrapper {
        > a {
          row-gap: 24px;
        }
      }

      &__info__text {
        .product-tag-label {
          margin-top: 8px;
        }
      }

      &__name {
        @include body-s($alt-font);
      }
    }
  }
}

@keyframes bounce {
  0% {
    transform: translateX(0);
    pointer-events: none;
  }
  50% {
    transform: translateX(var(--final-x));
    pointer-events: none;
  }
  100% {
    transform: translateX(0);
    pointer-events: auto;
  }
}
