import Component from '../abstracts/Component';
import TrackingComponent from './TrackingComponent';

export default class SizeSelectorComponent extends Component {

  get COMPONENTNAME() {
    return 'SizeSelectorComponent';
  }

  get SELECTORS() {
    return {
      size: '[data-size] input',
      sizeRadioBox: '[data-size]',
      wishlistSize: '[data-sizes-select] select',
      lowStockMsg: '[data-js-low-stock]',
      noSizeMsg: '[data-js-no-size]',
      selectedTemplate: '[data-size-selected-template]',
      unavailableTemplate: '[data-size-unavailable-template]',
    };
  }

  get CLASSES() {
    return {
      selected: 'selected',
    };
  }

  constructor(compEl) {
    super(compEl);
    this.TrackingComponent = new TrackingComponent();
  }

  readDOM() {
    return {
      box: this.$component,
      sizes: this.$component.querySelectorAll(this.SELECTORS.size),
      wishlistSize: this.$component.querySelector(this.SELECTORS.wishlistSize),
      lowStockMsg: this.$component.querySelector(this.SELECTORS.lowStockMsg),
      noSizeMsg: this.$component.querySelector(this.SELECTORS.noSizeMsg),
      selectedTemplate: this.$component.querySelector(this.SELECTORS.selectedTemplate),
      unavailableTemplate: this.$component.querySelector(this.SELECTORS.unavailableTemplate)
    };
  }

  // screen reader announce
  announceToScreenReader(message) {
    
    // aria-live region for screen reader announcements
    const liveRegion = document.createElement('div');
    liveRegion.id = 'size-selection-announcer';
    liveRegion.setAttribute('aria-live', 'assertive');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.classList.add('sr-only');
    document.body.appendChild(liveRegion);
    
    setTimeout(() => {
      liveRegion.textContent = message;
      
      // reset text announcement 
      setTimeout(() => {
        if (document.body.contains(liveRegion)) {
          document.body.removeChild(liveRegion);
        }
      }, 2000);
    }, 100);
  }

  // put classes on selected size and update aria-checked
  selectSize(size) {
    try {
      let prevSize = this.dom.box.querySelector('.selected');
      if (prevSize) {
        prevSize.classList.remove(this.CLASSES.selected);
        const prevInput = prevSize.querySelector('input');
        if (prevInput) {
          prevInput.setAttribute('aria-checked', 'false');
        }
      }
      
      size.closest(this.SELECTORS.sizeRadioBox).classList.add(this.CLASSES.selected);
      size.setAttribute('aria-checked', 'true');
  
      const sizeValue = size.getAttribute('data-size-value');
      const isUnavailable = size.getAttribute('data-unavailable') === 'true';
      
      let selectedTemplate = 'selected Size';
      let unavailableTemplate = 'out of stock';
      
      
      if (this.dom.selectedTemplate) {
        selectedTemplate = this.dom.selectedTemplate.getAttribute('data-size-selected-template') || selectedTemplate;
      }
      
      if (this.dom.unavailableTemplate) {
        unavailableTemplate = this.dom.unavailableTemplate.getAttribute('data-size-unavailable-template') || unavailableTemplate;
      }
      
      const message = isUnavailable ? 
        unavailableTemplate:
        selectedTemplate;
      //start screen reader announce
      this.announceToScreenReader(message);
    } catch (error) {
      console.error("Error in selectSize:", error);
    }
  }

  handleSizeSelection(size) {
    if (!size.disabled) {
      size.checked = true;
    }
    
    // put size
    this.selectSize(size);
    this.uncheckOtherSizes(size);
    
    // if not available launch notify me event
    if (size.dataset.unavailable === 'true') {
      // TODO: remove size from payloads?
      this.$emit(this.$customEvents.PRODUCT_EVENTS.notifyme, {size: size.getAttribute('data-product-code'), box: this.dom.box});
      this.$emit(this.$customEvents.CHANGE_RETURN_STEPS.disabledConfirm, {size: size});
    } else {
      // if available i want to hide the notifyme form
      this.$emit(this.$customEvents.PRODUCT_EVENTS.notifymeclose, {size: size.getAttribute('data-product-code'), box: this.dom.box});
      this.$emit(this.$customEvents.CHANGE_RETURN_STEPS.enableConfirm, {size: size});
    }
    
    if (size.dataset.lowStock === "true") {
      this.dom.lowStockMsg.classList.remove("--hidden");
    } else {
      this.dom.lowStockMsg.classList.add("--hidden");
    }
    
    // general event
    this.$emit(this.$customEvents.PRODUCT_EVENTS.sizechanged, {
      productCode: size.dataset.productCode,
      isWishlist: size.dataset.isWishlist
    });
    
    this.dom.noSizeMsg.classList.add("--hidden");
    
    // reset error
    // find error box
    let sizeErrorBox = document.querySelector('[data-sizes-error=prod_' + this.$component.dataset.productCode + ']');
    if (sizeErrorBox && !sizeErrorBox.classList.contains('--hidden')) {
      sizeErrorBox.classList.add('--hidden');
      sizeErrorBox.innerHTML = '';
    }
  }

  bindEvents() {
    let ctx = this;
    // pdp CTA
    if (this.dom.sizes.length) {
      let allSizesUnavailable = true;
      this.dom.sizes.forEach(size => {
        size.setAttribute('aria-checked', 'false');
        
        // mouse click management
        size.addEventListener('click', () => {
          this.handleSizeSelection(size);
        });
        
        // keyboard enter management for accessibility
        const label = size.closest('label');
        if (label) {
          label.addEventListener('keydown', (event) => {
            if (event.key === 'Enter' || event.key === ' ') {
              event.preventDefault();
              this.handleSizeSelection(size);
            }
          });
        }

        if (size.dataset.unavailable !== 'true') {
          allSizesUnavailable = false;
        }
      });

      window.addEventListener('load', () => {
        if (allSizesUnavailable) {
          // if every size is unavailable, fire event on load
          console.log("notifyme event");
          this.$emit(this.$customEvents.PRODUCT_EVENTS.notifyme, {unavailable: true, box: this.dom.box});
        }
      });
    }

    this.$on(this.$customEvents.SIZES_EVENTS.sizesupdate, ({payload}) => {
      this.dom.sizes.forEach(size => {
        if (size.dataset.productCode === payload.code) {
          size.dataset.isWishlist = payload.isWishlist;
        }
      });
    });

    this.$on(this.$customEvents.SIZES_EVENTS.noSizeSelected, () => {
      this.dom.noSizeMsg.classList.remove("--hidden");
    });
  }

  uncheckOtherSizes(selectedSize) {
    this.dom.sizes.forEach(size => {
      if (size !== selectedSize) {
        size.checked = false;
        size.setAttribute('aria-checked', 'false');
      }
    });
  }

  render() {
    this.log('Rendering...');
    this.dom = this.readDOM();
    this.bindEvents();
  }
}
