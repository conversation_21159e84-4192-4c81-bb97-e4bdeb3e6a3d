.c-main-header {
  &__pages {
    &__nav {
      &__list {
        display: flex;
      }
      &__item {
        @include body-s;
        &:hover {
          @include body-s($base-font, $font-600);
        }
        &.--sales {
          color: color(web-sale-red);
        }
      }
    }
  }
}

@media #{$until-large} {
  .c-main-header {
    &__logo {
      min-width: 160px;
    }
    &__pages {
      align-items: unset;
      position: absolute;
      padding: 20px 16px;
      left: 0;
      width: 100%;
      background-color: color(white);
      transition: transform 0.5s var(--anim-curve);
      overflow-y: auto;
      &.--opened {
        pointer-events: all;
        transform: translate(0, 0);
        bottom: 0;
      }
      &.--closed {
        pointer-events: none;
        transform: translate(-100%, 0);
        visibility: hidden;
      }
      &__nav {
        display: flex;
        flex-direction: column;
        flex: 1;
        row-gap: 24px;

        &__list {
          flex-direction: column;
          flex-wrap: nowrap;
          row-gap: 24px;
        }
        &__item {
          @include body-s;
          text-transform: uppercase;
          a {
            display: flex;
            justify-content: space-between;
          }
          &.--editorial {
            @include body-s;
          }
        }
      }
    }
    &__quick-links {
      @include body-xs;
      display: flex;
      flex-direction: column;
      row-gap: 24px;
      justify-content: flex-end;
      flex: 100%;
      .logged-user {
        @include body-s($base-font, $font-600);
        display: flex;
        justify-content: center;
        align-items: center;
        height: 24px;
        width: 24px;
        border-radius: 50%;
        background-color: color(light-blue);

        span {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 18px;
          width: 18px;
          border-radius: 50%;
        }
      }
      &__row {
        display: flex;
        align-items: center;
        column-gap: 8px;
      }
      &__wishlist {
        position: relative;
        &__counter {
          position: absolute;
          @include body-xxs($base-font, $font-600);
          top: -10px;
          right: -11px;
          width: 16px;
          height: 16px;
          background-color: color(light-blue);
          border-radius: 50%;
          text-align: center;
          &::before {
            content: attr(data-wishlist-qty);
          }
        }
      }
    }
  }
}

@media #{$from-large} {
  .c-main-header {
    &__logo {
      min-width: 215px;
    }
    &.--default {
      padding: 24px 32px;
      transition: all 0.5s var(--anim-curve);
    }
    &.--scrolled {
      transition: all 0.5s var(--anim-curve);
      padding: 16px 32px;
    }
    &__pages {
      transition: all 0.5s var(--anim-curve);
      margin: 32px 32px 0 32px;
      &.--scrolled {
        margin-top: 24px;
      }
      &__nav {
        display: flex;
        column-gap: 32px;
        &__list {
          flex-direction: row;
          flex-wrap: nowrap;
          column-gap: 32px;
        }
        &__item {
          font-family: $alt-font;
          &.--active {
            text-decoration: underline;
            text-underline-offset: 12px;
          }
          &:hover {
            font-family: $alt-font;
          }
        }
      }
    }
  }
}
