import Component from "../abstracts/Component";
import {doAjax} from "../utils/ajaxUtil";
import WishlistLogic from "../logics/WishlistLogic";
import TrackingComponent from "../components/TrackingComponent";
import {Select2Init} from "../utils/Select2Init";

export default class WishlistComponent extends Component {
  get SELECTORS() {
    return {
      wishBtn: ".js-wishlist-cta",
      wishBtnPaged: ".js-wishlist-cta:not(.component-loaded)",
      wishSizeSelectors: "[data-sizes-select] select",
    };
  }

  get CLASSES() {
    return {
      deleteBtnClass: "delete",
      addBtnClass: "js-add-to-wish",
      addedBtnClass: "js-added-wish",
      removeBtnClass: "js-remove-from-wish",
      error: "error",
    };
  }

  constructor(el) {
    super(el);
    // all wishlist button of
    this.wishBtn = document.querySelectorAll(this.SELECTORS.wishBtn);
    this.trackingComponent = new TrackingComponent();
    this.wishlistLogic = new WishlistLogic();
    this.wishSizeSelectors = document.querySelectorAll(this.SELECTORS.wishSizeSelectors);
  }

  // select change reset
  listenSelectWishSize() {
    let ctx = this;
    if (this.wishSizeSelectors != null) {
      this.wishSizeSelectors.forEach((select) => {
        select.addEventListener("change", (elem) => {
          // no default action
          elem.stopPropagation();
          // reset error
          if (select.parentElement.classList.contains(ctx.CLASSES.error)) {
            select.parentElement.classList.remove(ctx.CLASSES.error);
          }
          //TODO-not-working-properly
          /*if ( select.selectedOptions.length > 0 && typeof select.selectedOptions[0] !== 'undefined' ) {
            if (!select.selectedOptions[0].classList.contains('unavailable')) {
              // adding
              let url = select.getAttribute('data-wish-add-url');
              ctx.addToWishlist(select.selectedOptions[0],url);
            }
          }*/
        });
      });
    }
  }

  // listen to click
  listenBtnWish(btns) {
    // global
    let ctx = this;
    // if not passed
    if (typeof btns == "undefined") btns = this.wishBtn;
    //
    if (btns != null) {
      btns.forEach((btn) => {
        btn.addEventListener("click", (elem) => {
          // no default action
          elem.stopPropagation();
          // in base of button class
          if (btn.classList.contains(ctx.CLASSES.addBtnClass)) {
            // adding
            let url = btn.getAttribute("data-wish-add-url");
            ctx.addToWishlist(btn, url);
          } else {
            // removing
            let url = btn.getAttribute("data-wish-remove-url");
            ctx.removeToWishlist(btn, url);
          }
        });
      });
    }
  }

  //
  addToWishlist(elem, url) {
    // vars
    let ctx = this;
    let code = elem.dataset.code;
    // ajax
    doAjax({
      url: url,
      data: {
        code: code,
      },
      dataType: "html",
      success: function (resp) {
        ctx.log("added to wishlist", code);
        // n of items on wishlist
        let qty = resp;
        // event
        ctx.$emit(ctx.$customEvents.PRODUCT_EVENTS.addedtowish, {elem: elem, qty: qty});
        ctx.trackingComponent.trackAction("wishlistAdd", elem.dataset, elem);
        elem.setAttribute("aria-label",elem.dataset.labelRemove);
      },
      error: function (xhr) {
        console.log("error in add to wishlist");
      },
    });
  }

  //
  removeToWishlist(elem, url) {
    // vars
    let ctx = this;
    let code = elem.dataset.code;
    // ajax
    doAjax({
      url: url,
      dataType: "json",
      type: "POST",
      success: function (resp) {
        // n of items on wishlist
        let qty = resp.count;
        // launch event
        ctx.$emit(ctx.$customEvents.PRODUCT_EVENTS.removedfromwish, {elem: elem, qty: qty});
        elem.setAttribute("aria-label",elem.dataset.labelAdd);

      },
      error: function (xhr) {
        console.log(xhr);
      },
    });
  }

  // wishlist check
  wishlistCheckAjax(code) {
    return new Promise((resolve, reject) => {
      let data = {};
      data[code] = code;
      //
      let contextPath = document.getElementsByTagName("HTML")[0].getAttribute("data-context");
      $.ajax({
        url: contextPath + "/action/ajax/wishlist-check-product",
        data: {
          code: code,
        },
        success: function (result) {
          // resolve
          resolve(result);
        },
      });
    });
  }

  updateWishButton(btn, disable) {
    // check
    if (btn == null) return false;
    //
    if (disable === true) {
      // disable button
      btn.classList.remove(this.CLASSES.addBtnClass);
      btn.classList.add(this.CLASSES.addedBtnClass);
      btn.classList.add(this.CLASSES.removeBtnClass);
    } else {
      // enable button
      btn.classList.add(this.CLASSES.addBtnClass);
      btn.classList.remove(this.CLASSES.addedBtnClass);
      btn.classList.remove(this.CLASSES.removeBtnClass);
    }
  }

  bindEvents() {
    let ctx = this;
    // addedtowish managed by wishlistLogic

    // event on remove from wish
    this.$on(this.$customEvents.PRODUCT_EVENTS.removedfromwish, ({event, payload}) => {
      // btn
      let btn = payload.elem;
      // if it's the delete button on wishlist page
      if (btn.classList.contains(ctx.CLASSES.deleteBtnClass)) {
        let code = btn.dataset.code;
        let prodCard = ctx.$component.querySelector('[data-card-code="' + code + '"]');
        if (prodCard != null) prodCard.remove();
      } else {
        // managed by WishlistLogic
      }
    });

    // event on select size - i want to wishlist the product with the size
    this.$on(this.$customEvents.PRODUCT_EVENTS.sizechanged, ({event, payload}) => {
      // search the btn with the base product code
      let btnWish = document.querySelector('[data-wishlist-code="' + payload.productCode + '"]');
      if (btnWish) {
        // put in the button the size code
        btnWish.dataset.code = payload.productCode;
        // check size on size select
        // go promise
        ctx.wishlistCheckAjax(payload.productCode).then((result) => {
          // result: if is in WL i disanble the button, else enable it
          ctx.updateWishButton(btnWish, result === "true");
        });
      }
    });

    // event simplebar loader
    this.$on(this.$customEvents.SIMPLEBAR_EVENTS.loaded, ({event, payload}) => {
      // let's search the wish buttons
      let btns = payload.elem.querySelectorAll(ctx.SELECTORS.wishBtn);
      if (btns != null) {
        ctx.listenBtnWish(btns);
      }
    });

    // Size not selected event
    this.$on(this.$customEvents.PRODUCT_EVENTS.sizerequired, (e) => {
      // find error box
      let sizeBox = document.querySelector("[data-sizes-code=prod_" + e.payload.getAttribute("data-code") + "]");
      if (sizeBox != null) {
        sizeBox.classList.add(ctx.CLASSES.error);
      }
    });

    // listing page when launched the filters i need to rebind all buttons
    this.$on(this.$customEvents.FILTERS_EVENTS.updated, ({event, payload}) => {
      // let's search the wish buttons
      let btns = document.querySelectorAll(ctx.SELECTORS.wishBtn);
      if (btns != null) {
        ctx.listenBtnWish(btns);
      }
    });

    // listing when fired pagination and infinite scroll
    this.$on(this.$customEvents.INFINITE_SCROLL_EVENTS.loaded, ({event, payload}) => {
      // let's search the wish buttons
      let btns = document.querySelectorAll(ctx.SELECTORS.wishBtnPaged);
      if (btns != null) {
        ctx.listenBtnWish(btns);
      }
    });
  }

  render() {
    this.log("Rendering...");
    this.bindEvents();
    // add btn
    this.listenBtnWish();
    // select reset
    this.listenSelectWishSize();
    // wishlist logic
    this.wishlistLogic.render();
    //init select2
    Select2Init(); // Only custom select
  }
}
