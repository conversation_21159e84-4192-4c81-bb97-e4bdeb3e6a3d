/* REGISTER PAGE */
.p-register {
  &__wrapper {
    @include page-max-width;
    @include grid;
    grid-row-gap: 32px;
    margin: 16px 8px 32px;
  }
  &__col,
  &__reg-side {
    @include col(4);
  }
  &__form {
    &__title {
      @include h6;
      margin-bottom: 16px;
      // CONFIRM PAGE
      &.--confirm {
        @include h7;
        margin-bottom: 24px;
      }
    }
    &__subtitle {
      @include body-s($alt-font);
      margin-bottom: 32px;
      // CONFIRM PAGE
      &.--confirm {
        margin-bottom: 0;
      }
    }
  }
  &__reg-side {
    background: color(grey);
    padding: 25px 12px;
  }
  &__reg-side {
    display: flex;
    flex-direction: column;
    row-gap: 12px;
    &__title {
      @include h8;
    }
  }
  // CONFIRM PAGE
  &__confirm {
    #wrapper {
      margin: 64px 16px;
      @include page-max-width;
    }
    &__wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      .icon-email {
        @include icon-size(42);
        margin-bottom: 32px;
      }
    }
  }
}

@media #{$from-medium} {
  .p-register {
    &__col,
    &__reg-side {
      @include col(12);
    }
  }
}

@media #{$from-large} {
  .p-register {
    &__wrapper {
      margin: 56px auto 80px;
    }
    &__col {
      @include col(5, 1);
    }
    &__reg-side {
      @include col(4, 7);
      height: fit-content;
    }
    // CONFIRM PAGE
    &__confirm {
      #wrapper {
        @include grid();
        margin: 96px auto;
      }
      &__wrapper {
        @include col(6, 3);
      }
    }
  }
}

@media #{$from-extra-large} {
  .p-register {
    &__col {
      @include col(6, 1);
    }
    &__reg-side {
      @include col(3, 8);
    }
  }
}
