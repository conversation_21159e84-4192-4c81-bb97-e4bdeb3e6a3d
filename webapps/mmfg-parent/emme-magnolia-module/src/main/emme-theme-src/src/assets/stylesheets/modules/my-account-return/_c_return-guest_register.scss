@import "../../modules/customer/c_form-wrapper";

/* GUEST RETURN REGISTER */
.p-register {
  &__wrapper {
    display: flex;
    // adapt register html to guest return style
    flex-direction: column-reverse;
    justify-content: flex-end;
    gap: 24px;
    background-color: color(grey);
    padding: 24px;
  }
  &__reg-side {
    display: flex;
    flex-direction: column;
    &__title {
      @include body-m($base-font, $font-600);
      margin-bottom: 24px;
    }
  }
  &__form {
    &__title {
      @include h6;
      margin-bottom: 16px;
    }
    &__subtitle {
      @include body-s;
      margin-bottom: 32px;
    }
  }
}

// _c_registration-form override
.registration {
  &__form {
    row-gap: 24px;
    &__wrapper {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }
  }
}

@media #{$from-large} {
  .p-register {
    &__reg-side {
      height: fit-content;
    }
  }
}

