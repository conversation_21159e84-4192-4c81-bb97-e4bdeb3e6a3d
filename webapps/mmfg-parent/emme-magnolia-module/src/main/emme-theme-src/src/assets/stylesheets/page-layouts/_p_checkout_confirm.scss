.p-checkout.--confirm {
  .order-confirm {
    display: flex;
    flex-direction: column;
    row-gap: 32px;
    padding: 24px 16px;
    &__message {
      .title {
        @include body-m;
        margin-bottom: 8px;
      }
      .subtitle {
        @include body-xxs($alt-font);
      }
    }
    &__number {
      display: flex;
      flex-direction: column;
      gap: 12px;
      .label {
        @include body-s;
      }
      .value {
        @include body-s($alt-font);
        width: fit-content;
        padding: 4px 16px;
      }
    }
  }
  .p-register {
    &__wrapper {
      margin-top: -4px;
      display: flex;
      flex-direction: column;
      gap: 16px;
      & > * {
        flex: 0 1 100%;
      }
    }
    &__form {
      padding: 25px 0;
      display: flex;
      flex-direction: column;
      row-gap: 24px;
      &__title {
        @include h8;
      }
      &__subtitle {
        @include body-xs($alt-font);
      }
    }
    &__reg-side {
      background: color(grey);
      padding: 25px 12px;
      &__title {
        @include h8;
        margin-bottom: 24px;
      }
    }
  }
  .checkout {
    &__content {
      row-gap: 16px;
    }
  }
}


@media #{$from-large} {
  .p-checkout.--confirm {
    .order-confirm {
      &__number {
        flex-direction: row;
        align-items: center;
      }
    }
    .p-register {
      &__wrapper {
        flex-direction: row;
      }
    }
  }
}
